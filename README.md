# ADC Credit System

A comprehensive API credit management system that allows you to easily integrate credit-based API access into your applications. This system provides a way to manage API keys, track usage, and limit access based on subscription tiers.

## Features

- **API Key Management**: Generate and manage secure API keys with fine-grained permissions
- **Credit System**: Track and manage API usage credits with subscription-based tiers
- **Usage Analytics**: Detailed analytics and reporting on API usage and credit consumption
- **Subscription Tiers**: Different subscription plans with varying credit limits
- **Google OAuth Authentication**: Secure authentication using Google accounts
- **Modern UI**: Built with Next.js, Tailwind CSS, and shadcn UI components

## Tech Stack

### Frontend
- **Next.js**: React framework for building the web application
- **Bun**: Fast JavaScript runtime and package manager
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: High-quality UI components built with Radix UI and Tailwind
- **NextAuth.js**: Authentication for Next.js applications

### Backend
- **Go (Golang)**: Backend API server
- **Gin**: HTTP web framework for Go
- **GORM**: ORM library for Go
- **PostgreSQL**: Database for storing user data, API keys, and usage information
- **JWT**: JSON Web Tokens for authentication

## Getting Started

### Prerequisites

- Node.js (v18 or later) - for local development
- Go (v1.20 or later) - for local development
- PostgreSQL
- Docker and Docker Compose (optional, for containerized deployment)

### Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/adc-credit.git
   cd adc-credit
   ```

2. Set up the frontend:
   ```bash
   # Install dependencies
   npm install

   # Create .env.local file
   cp .env.local.example .env.local
   ```

3. Set up the backend:
   ```bash
   cd backend

   # Create .env file
   cp .env.example .env

   # Initialize the database
   # Update the .env file with your PostgreSQL credentials
   ```

4. Set up Google OAuth:
   - Create a project in the [Google Cloud Console](https://console.cloud.google.com/)
   - Set up OAuth credentials
   - Add the client ID and secret to both `.env.local` and `backend/.env` files

### Running the Application

#### Option 1: Using Docker (Recommended)

1. Make sure you have Docker and Docker Compose installed on your system.

2. Create a `.env` file in the project root with the required environment variables:
   ```bash
   # Database Configuration
   DATABASE_URL=postgresql://username:password@host:port/database

   # Authentication
   JWT_SECRET=your-jwt-secret
   NEXTAUTH_SECRET=your-nextauth-secret
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret

   # Application URLs
   FRONTEND_URL=http://localhost:3800
   BACKEND_URL=http://localhost:8080

   # Scheduled Credits
   SCHEDULER_API_KEY=your-secure-api-key
   ```

3. Start the application using Docker Compose:
   ```bash
   docker-compose up -d
   ```

4. Open [http://localhost:3800](http://localhost:3800) in your browser

#### Option 2: Running Locally

1. Start the backend server:
   ```bash
   cd backend
   go run cmd/api/main.go
   ```

2. Start the frontend development server:
   ```bash
   # From the root directory
   npm run dev
   ```

3. Open [http://localhost:3800](http://localhost:3800) in your browser

## API Integration

### Verifying API Keys

```bash
curl -X POST https://api.example.com/v1/external/verify \
  -H "Content-Type: application/json" \
  -d '{"api_key": "your_api_key", "credits": 1}'
```

### Consuming Credits

```bash
curl -X POST https://api.example.com/v1/external/consume \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{"endpoint": "/api/data", "method": "GET", "credits": 1}'
```

## Docker Deployment

The application includes Docker configuration for both development and production environments. The frontend uses Bun for faster builds and runtime performance.

### Development

To run the application in a development environment:

```bash
docker-compose up -d
```

This will start both the frontend (using Bun) and backend services in development mode.

### Production

For production deployment:

1. Build the Docker images:
   ```bash
   docker build -t adc-credit-backend ./backend
   docker build -t adc-credit-frontend .
   ```

2. Run the containers with appropriate environment variables:
   ```bash
   docker run -d -p 8080:8080 --env-file .env --name adc-backend adc-credit-backend
   docker run -d -p 3800:3800 --env-file .env --name adc-frontend adc-credit-frontend
   ```

### Docker Image Management

To manage your Docker images:

- List all images: `docker images`
- Remove an image: `docker rmi <image-id>`
- List running containers: `docker ps`
- Stop a container: `docker stop <container-id>`
- Remove a container: `docker rm <container-id>`

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [Bun](https://bun.sh/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.com/)
- [NextAuth.js](https://next-auth.js.org/)
- [Gin](https://gin-gonic.com/)
- [GORM](https://gorm.io/)
- [Docker](https://www.docker.com/)
