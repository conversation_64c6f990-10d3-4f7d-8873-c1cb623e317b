{"name": "adc-credit", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --experimental-https --port 3800", "build": "next build", "build:docker": "NODE_ENV=production ESLINT_CONFIG_FILE=.eslintrc.js TS_NODE_PROJECT=tsconfig.build.json next build", "start": "next start", "start:server": "node server.js", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.8.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tabler/icons-react": "^3.33.0", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "express": "^5.1.0", "framer-motion": "^12.12.1", "html5-qrcode": "^2.3.8", "lucide-react": "^0.511.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "pg": "^8.16.0", "phosphor-react": "^1.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-redux": "^9.2.0", "recharts": "^2.15.3", "shiki": "^3.4.2", "sonner": "^2.0.3", "stripe": "^18.1.0", "tailwind-merge": "^3.3.0", "textarea": "^0.3.0", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}