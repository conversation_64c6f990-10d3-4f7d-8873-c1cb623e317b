/**
 * ADC Credit SDK Types
 */

// Re-export types from the main application
export type {
  APIKey,
  Subscription,
  SubscriptionTier,
  Usage,
  UsageSummary,
  Webhook,
  WebhookDelivery,
  AnalyticsSummary,
  AnalyticsTrend,
  EndpointAnalytics,
  PerformanceMetrics,
  Transaction,
  MerchantShop,
  ShopCustomer,
  CreditCode,
  ShopCreditTransaction,
} from '@/types';

// Export types from the SDK modules
export type { User, ExternalUser } from './users';
export type { Organization, Branch } from './organizations';
export type {
  CreateShopRequest,
  UpdateShopRequest,
  AddCustomerRequest,
  AddShopCreditRequest,
  GenerateCreditCodeRequest
} from './merchant';
export type {
  ShopWithCredit,
  RedeemCreditCodeRequest,
  UseShopCreditRequest
} from './customer';
export type {
  CreateSubscriptionRequest,
  UpdateSubscriptionRequest
} from './subscription';

/**
 * SDK Configuration options
 */
export interface ADCCreditConfig {
  /**
   * API URL (defaults to http://localhost:8080/api/v1)
   */
  apiUrl?: string;

  /**
   * API Key for authentication
   */
  apiKey?: string;

  /**
   * JWT token for authentication (alternative to apiKey)
   */
  token?: string;

  /**
   * Debug mode - enables verbose logging
   */
  debug?: boolean;
}

/**
 * API Response wrapper
 */
export interface APIResponse<T> {
  data?: T;
  error?: string;
  status: number;
}

/**
 * Credit verification request
 */
export interface VerifyAPIKeyRequest {
  api_key: string;
  credits: number;
}

/**
 * Credit verification response
 */
export interface VerifyAPIKeyResponse {
  valid: boolean;
  credit_balance: number;
}

/**
 * Credit consumption request
 */
export interface ConsumeCreditsRequest {
  endpoint: string;
  method: string;
  credits: number;
  ip_address?: string;
  user_agent?: string;
}

/**
 * Credit consumption response
 */
export interface ConsumeCreditsResponse {
  message: string;
  credit_balance: number;
  usage: Usage;
}

/**
 * Create API Key request
 */
export interface CreateAPIKeyRequest {
  name: string;
  permissions?: string[];
}

/**
 * Update API Key request
 */
export interface UpdateAPIKeyRequest {
  name?: string;
  enabled?: boolean;
  permissions?: string[];
}

/**
 * Create Webhook request
 */
export interface CreateWebhookRequest {
  name: string;
  url: string;
  secret: string;
  events: string[];
}

/**
 * Update Webhook request
 */
export interface UpdateWebhookRequest {
  name?: string;
  url?: string;
  secret?: string;
  events?: string[];
  active?: boolean;
}

/**
 * Add Credits request
 */
export interface AddCreditsRequest {
  amount: number;
  description?: string;
  reference?: string;
}

/**
 * Add Credits response
 */
export interface AddCreditsResponse {
  message: string;
  credit_balance: number;
  transaction: Transaction;
}
