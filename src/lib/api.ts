import { APIKey, AnalyticsSummary, AnalyticsTrend, EndpointAnalytics, PerformanceMetrics, Subscription, SubscriptionTier, Transaction, Usage, UsageSummary, Webhook, WebhookDelivery } from "@/types";

// Ensure API_URL always has the /api/v1 prefix
const BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL ;
const API_URL = BASE_URL.endsWith('/api/v1') ? BASE_URL : `${BASE_URL}/api/v1`;

// Helper function for API requests
async function fetchAPI<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  // Check if the endpoint starts with a slash
  if (!endpoint.startsWith('/')) {
    console.error('Endpoint should start with a slash:', endpoint);
    endpoint = '/' + endpoint;
  }

  // If the endpoint already starts with /api/v1, don't add the API_URL prefix
  const url = endpoint.startsWith('/api/v1')
    ? `${BASE_URL}${endpoint.substring(7)}` // Remove the /api/v1 prefix
    : `${API_URL}${endpoint}`;
  console.log('Fetching from URL:', url);
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'An error occurred');
  }

  return response.json();
}

// Authentication
export async function authenticateWithGoogle(token: string): Promise<{ token: string; refresh_token: string; user: any }> {
  return fetchAPI('/auth/google', {
    method: 'POST',
    body: JSON.stringify({ token }),
  });
}

export async function refreshToken(refreshToken: string): Promise<{ token: string; refresh_token: string }> {
  return fetchAPI('/auth/refresh', {
    method: 'POST',
    body: JSON.stringify({ refresh_token: refreshToken }),
  });
}

// User
export async function getCurrentUser(token: string): Promise<any> {
  return fetchAPI('/users/me', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function updateUser(token: string, data: { name?: string; picture?: string }): Promise<any> {
  return fetchAPI('/users/me', {
    method: 'PUT',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
}

// API Keys
export async function getAPIKeys(token: string): Promise<APIKey[]> {
  return fetchAPI('/apikeys', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getAPIKey(token: string, id: string): Promise<APIKey> {
  return fetchAPI(`/apikeys/${id}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function createAPIKey(token: string, data: { name: string; permissions?: string[] }): Promise<APIKey> {
  return fetchAPI('/apikeys', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
}

export async function updateAPIKey(token: string, id: string, data: { name?: string; enabled?: boolean; permissions?: string[] }): Promise<APIKey> {
  return fetchAPI(`/apikeys/${id}`, {
    method: 'PUT',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
}

export async function deleteAPIKey(token: string, id: string): Promise<void> {
  return fetchAPI(`/apikeys/${id}`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

// Credits
export async function getCreditBalance(token: string): Promise<{ credit_balance: number; credit_limit: number; subscription: Subscription }> {
  return fetchAPI('/credits', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function addCredits(token: string, data: { amount: number; description?: string; reference?: string }): Promise<{ message: string; credit_balance: number; transaction: Transaction }> {
  return fetchAPI('/credits/add', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
}

export async function getTransactions(token: string): Promise<Transaction[]> {
  return fetchAPI('/credits/transactions', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getNextScheduledCreditDate(token: string): Promise<{ next_scheduled_credit_date: string }> {
  return fetchAPI('/credits/scheduled/next', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getScheduledCreditHistory(token: string): Promise<Transaction[]> {
  return fetchAPI('/credits/scheduled/history', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function processScheduledCredits(token: string): Promise<{ message: string; count: number }> {
  return fetchAPI('/admin/credits/scheduled/process', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function manuallyAddScheduledCredits(token: string, userId: string): Promise<{ message: string; user_id: string; credit_balance: number }> {
  return fetchAPI('/admin/credits/scheduled/manual', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ user_id: userId }),
  });
}

// Usage
export async function getUsage(token: string, params?: { start_date?: string; end_date?: string; api_key_id?: string }): Promise<Usage[]> {
  const queryParams = new URLSearchParams();
  if (params?.start_date) queryParams.append('start_date', params.start_date);
  if (params?.end_date) queryParams.append('end_date', params.end_date);
  if (params?.api_key_id) queryParams.append('api_key_id', params.api_key_id);

  const query = queryParams.toString() ? `?${queryParams.toString()}` : '';

  return fetchAPI(`/usage${query}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getUsageSummary(token: string, params?: { period?: string; start_date?: string; end_date?: string }): Promise<UsageSummary> {
  const queryParams = new URLSearchParams();
  if (params?.period) queryParams.append('period', params.period);
  if (params?.start_date) queryParams.append('start_date', params.start_date);
  if (params?.end_date) queryParams.append('end_date', params.end_date);

  const query = queryParams.toString() ? `?${queryParams.toString()}` : '';

  return fetchAPI(`/usage/summary${query}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

// Subscriptions
export async function getSubscriptionTiers(token: string): Promise<SubscriptionTier[]> {
  return fetchAPI('/subscriptions/tiers', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getSubscriptions(token: string): Promise<Subscription[]> {
  return fetchAPI('/subscriptions', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function createSubscription(token: string, data: { subscription_tier_id: number; auto_renew?: boolean }): Promise<Subscription> {
  return fetchAPI('/subscriptions', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
}

export async function updateSubscription(token: string, id: string, data: { auto_renew: boolean }): Promise<Subscription> {
  return fetchAPI(`/subscriptions/${id}`, {
    method: 'PUT',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
}

export async function cancelSubscription(token: string, id: string): Promise<{ message: string }> {
  return fetchAPI(`/subscriptions/${id}`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

// Webhooks
export async function getWebhooks(token: string): Promise<Webhook[]> {
  return fetchAPI('/webhooks', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getWebhook(token: string, id: string): Promise<Webhook> {
  return fetchAPI(`/webhooks/${id}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function createWebhook(token: string, data: { name: string; url: string; secret?: string; events: string[] }): Promise<Webhook> {
  return fetchAPI('/webhooks', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
}

export async function updateWebhook(token: string, id: string, data: { name?: string; url?: string; secret?: string; events?: string[]; active?: boolean }): Promise<Webhook> {
  return fetchAPI(`/webhooks/${id}`, {
    method: 'PUT',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
}

export async function deleteWebhook(token: string, id: string): Promise<{ message: string }> {
  return fetchAPI(`/webhooks/${id}`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getWebhookDeliveries(token: string, id: string): Promise<WebhookDelivery[]> {
  return fetchAPI(`/webhooks/${id}/deliveries`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

// Advanced Analytics
export async function getAnalyticsSummary(token: string, params?: { start_date?: string; end_date?: string }): Promise<AnalyticsSummary> {
  const queryParams = new URLSearchParams();
  if (params?.start_date) queryParams.append('start_date', params.start_date);
  if (params?.end_date) queryParams.append('end_date', params.end_date);

  const query = queryParams.toString() ? `?${queryParams.toString()}` : '';

  return fetchAPI(`/analytics/summary${query}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getAnalyticsTrends(token: string, params?: { interval?: string; start_date?: string; end_date?: string }): Promise<AnalyticsTrend[]> {
  const queryParams = new URLSearchParams();
  if (params?.interval) queryParams.append('interval', params.interval);
  if (params?.start_date) queryParams.append('start_date', params.start_date);
  if (params?.end_date) queryParams.append('end_date', params.end_date);

  const query = queryParams.toString() ? `?${queryParams.toString()}` : '';

  return fetchAPI(`/analytics/trends${query}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getEndpointAnalytics(token: string, params?: { start_date?: string; end_date?: string }): Promise<EndpointAnalytics[]> {
  const queryParams = new URLSearchParams();
  if (params?.start_date) queryParams.append('start_date', params.start_date);
  if (params?.end_date) queryParams.append('end_date', params.end_date);

  const query = queryParams.toString() ? `?${queryParams.toString()}` : '';

  return fetchAPI(`/analytics/endpoints${query}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

export async function getPerformanceMetrics(token: string, params?: { metric?: string; start_date?: string; end_date?: string }): Promise<PerformanceMetrics> {
  const queryParams = new URLSearchParams();
  if (params?.metric) queryParams.append('metric', params.metric);
  if (params?.start_date) queryParams.append('start_date', params.start_date);
  if (params?.end_date) queryParams.append('end_date', params.end_date);

  const query = queryParams.toString() ? `?${queryParams.toString()}` : '';

  return fetchAPI(`/analytics/performance${query}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

// External API (for integration examples)
export async function verifyAPIKey(apiKey: string, credits: number): Promise<{ valid: boolean; credit_balance: number }> {
  return fetchAPI('/external/verify', {
    method: 'POST',
    body: JSON.stringify({ api_key: apiKey, credits }),
  });
}

export async function consumeCredits(apiKey: string, data: { endpoint: string; method: string; credits: number; ip_address?: string; user_agent?: string }): Promise<{ message: string; credit_balance: number; usage: Usage }> {
  return fetchAPI('/external/consume', {
    method: 'POST',
    headers: {
      'X-API-Key': apiKey,
    },
    body: JSON.stringify(data),
  });
}
