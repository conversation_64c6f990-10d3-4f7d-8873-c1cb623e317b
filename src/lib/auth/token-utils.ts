/**
 * Utility functions for token management
 */

/**
 * Exchanges a Google OAuth token for a backend JWT token
 * @param googleToken The Google OAuth token
 * @returns The backend JWT token
 */
export async function exchangeGoogleToken(googleToken: string): Promise<{
  token: string;
  refreshToken: string;
  user: any;
}> {
  const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8080';
  console.log(`Token Utils: Exchanging Google token at ${apiUrl}/api/v1/auth/google`);

  // Clean the token if it has any whitespace
  const cleanToken = googleToken.trim();

  // Log the token for debugging (first 20 chars)
  console.log(`Token Utils: Token starts with: ${cleanToken.substring(0, 20)}...`);

  try {
    const response = await fetch(`${apiUrl}/api/v1/auth/google`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token: cleanToken }),
    });

    console.log(`Token Utils: Exchange response status: ${response.status}`);

    // Get response as text first for debugging
    const responseText = await response.text();
    console.log(`Token Utils: Response text: ${responseText.substring(0, 100)}...`);

    if (!response.ok) {
      console.error('Token Utils: Error exchanging token:', responseText);
      throw new Error(responseText);
    }

    // Parse the response text as JSON
    let data;
    try {
      data = JSON.parse(responseText);
    } catch (e) {
      console.error('Token Utils: Error parsing response:', e);
      throw new Error('Invalid JSON response from server');
    }

    console.log('Token Utils: Token exchange successful');
    console.log('Token Utils: Received token:', data.token ? `${data.token.substring(0, 20)}...` : 'No token');

    return data;
  } catch (error) {
    console.error('Token Utils: Error in exchange process:', error);
    throw error;
  }
}

/**
 * Stores a backend JWT token in session storage and localStorage
 * @param token The backend JWT token
 * @param user The user object
 */
export function storeToken(token: string, refreshToken: string, user: any): void {
  console.log('Token Utils: Storing token in sessionStorage and localStorage');

  // Store in sessionStorage (for current session)
  sessionStorage.setItem('accessToken', token);
  sessionStorage.setItem('refreshToken', refreshToken);
  sessionStorage.setItem('currentUser', JSON.stringify(user));

  // Also store in localStorage (for persistence across sessions)
  localStorage.setItem('accessToken', token);
  localStorage.setItem('refreshToken', refreshToken);
  localStorage.setItem('currentUser', JSON.stringify(user));
}

/**
 * Gets the stored token from sessionStorage or localStorage
 * @returns The stored token or null if not found
 */
export function getStoredToken(): string | null {
  // Try sessionStorage first
  const sessionToken = sessionStorage.getItem('accessToken');
  if (sessionToken) {
    return sessionToken;
  }

  // Fall back to localStorage
  const localToken = localStorage.getItem('accessToken');
  if (localToken) {
    // Copy to sessionStorage for future use
    sessionStorage.setItem('accessToken', localToken);
    return localToken;
  }

  return null;
}

/**
 * Clears all stored tokens and user data
 */
export function clearTokens(): void {
  sessionStorage.removeItem('accessToken');
  sessionStorage.removeItem('refreshToken');
  sessionStorage.removeItem('currentUser');

  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('currentUser');
}
