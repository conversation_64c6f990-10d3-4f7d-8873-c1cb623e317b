/**
 * Client utility for making requests through the Next.js proxy API
 * This provides a convenient way to call backend APIs through the proxy
 */

// Base URL for the proxy API
const PROXY_BASE_URL = '/api/v1';

/**
 * Helper function to get stored auth token
 */
function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  
  // Try sessionStorage first, then localStorage
  return sessionStorage.getItem('accessToken') || localStorage.getItem('accessToken');
}

/**
 * Generic proxy request function
 */
async function proxyRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  // Ensure endpoint starts with /
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const url = `${PROXY_BASE_URL}${cleanEndpoint}`;

  // Get auth token
  const token = getAuthToken();

  // Prepare headers
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // Add auth token if available
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  console.log(`Making proxy request to: ${url}`);

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    // Handle non-JSON responses
    const contentType = response.headers.get('content-type');
    let data;

    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    if (!response.ok) {
      console.error(`Proxy request failed: ${response.status}`, data);
      throw new Error(data?.error || data?.message || `HTTP ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('Proxy request error:', error);
    throw error;
  }
}

/**
 * Convenience methods for different HTTP verbs
 */
export const proxyApi = {
  /**
   * GET request through proxy
   */
  get: <T = any>(endpoint: string, params?: Record<string, string>): Promise<T> => {
    const url = new URL(endpoint, 'http://localhost');
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }
    
    return proxyRequest<T>(url.pathname + url.search, {
      method: 'GET',
    });
  },

  /**
   * POST request through proxy
   */
  post: <T = any>(endpoint: string, data?: any): Promise<T> => {
    return proxyRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  },

  /**
   * PUT request through proxy
   */
  put: <T = any>(endpoint: string, data?: any): Promise<T> => {
    return proxyRequest<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  },

  /**
   * DELETE request through proxy
   */
  delete: <T = any>(endpoint: string): Promise<T> => {
    return proxyRequest<T>(endpoint, {
      method: 'DELETE',
    });
  },

  /**
   * PATCH request through proxy
   */
  patch: <T = any>(endpoint: string, data?: any): Promise<T> => {
    return proxyRequest<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  },
};

/**
 * Example usage functions for common API endpoints
 */
export const proxyExamples = {
  // User endpoints
  getCurrentUser: () => proxyApi.get('/users/me'),
  updateUser: (data: any) => proxyApi.put('/users/me', data),

  // Organization endpoints
  getOrganizations: () => proxyApi.get('/organizations'),
  createOrganization: (data: any) => proxyApi.post('/organizations', data),
  getOrganization: (slug: string) => proxyApi.get(`/organizations/${slug}`),

  // API Keys
  getApiKeys: () => proxyApi.get('/apikeys'),
  createApiKey: (data: any) => proxyApi.post('/apikeys', data),

  // Merchant shops
  getMerchantShops: () => proxyApi.get('/merchant-shops'),
  createMerchantShop: (data: any) => proxyApi.post('/merchant-shops', data),
  getMerchantShop: (id: string) => proxyApi.get(`/merchant-shops/${id}`),

  // Credits
  getCredits: () => proxyApi.get('/credits'),
  addCredits: (data: any) => proxyApi.post('/credits/add', data),

  // Subscriptions
  getSubscriptions: () => proxyApi.get('/subscriptions'),
  getSubscriptionTiers: () => proxyApi.get('/subscriptions/tiers'),
};

export default proxyApi;
