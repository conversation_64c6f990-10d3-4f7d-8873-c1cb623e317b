import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';


export async function POST(req: NextRequest) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the access token from the session
    const accessToken = session.accessToken || (session.user as any).accessToken;

    if (!accessToken) {
      console.error('No access token found in session:', session);
      return NextResponse.json(
        { error: 'No access token available' },
        { status: 401 }
      );
    }

    // Get the request body
    const body = await req.json();

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

    console.log(`Forwarding request to ${apiUrl}/api/stripe/create-checkout-session`);
    console.log(`Using access token: ${accessToken.substring(0, 10)}...`);

    // Forward the request to the backend API
    const response = await fetch(`${apiUrl}/api/stripe/create-checkout-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify(body),
    });

    // Check if the response is OK
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);
      return NextResponse.json(
        { error: `Backend API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    // Try to parse the response as JSON
    let data;
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json();
      } catch (e) {
        console.error('Error parsing JSON response:', e);
        const text = await response.text();
        console.error('Response text:', text);
        return NextResponse.json(
          { error: 'Invalid JSON response from backend' },
          { status: 500 }
        );
      }
    } else {
      // Handle non-JSON response
      const text = await response.text();
      console.log('Non-JSON response:', text);
      data = { message: text };
    }

    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
