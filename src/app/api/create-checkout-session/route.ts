import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import Stripe from 'stripe';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2025-04-30.basil',
});

export async function POST(req: NextRequest) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'You must be logged in to create a checkout session' },
        { status: 401 }
      );
    }

    // Get the request body
    const body = await req.json();
    const {
      priceId,
      productName,
      tierId,
      subscriptionType = 'personal',
      merchantShopId,
      shopCustomerId
    } = body;

    if (!priceId || !productName || !tierId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Validate subscription type
    if (!['personal', 'merchant', 'customer'].includes(subscriptionType)) {
      return NextResponse.json(
        { error: 'Invalid subscription type' },
        { status: 400 }
      );
    }

    // Validate merchant shop ID if subscription type is merchant
    if (subscriptionType === 'merchant' && !merchantShopId) {
      return NextResponse.json(
        { error: 'Merchant shop ID is required for merchant subscriptions' },
        { status: 400 }
      );
    }

    // Validate shop customer ID if subscription type is customer
    if (subscriptionType === 'customer' && !shopCustomerId) {
      return NextResponse.json(
        { error: 'Shop customer ID is required for customer subscriptions' },
        { status: 400 }
      );
    }

    // Get the success and cancel URLs
    const origin = req.headers.get('origin') || 'https://localhost:3800';
    const successUrl = `${origin}/dashboard/subscriptions?success=true&session_id={CHECKOUT_SESSION_ID}&tier_id=${tierId}&subscription_type=${subscriptionType}${merchantShopId ? `&merchant_shop_id=${merchantShopId}` : ''}${shopCustomerId ? `&shop_customer_id=${shopCustomerId}` : ''}`;
    const cancelUrl = `${origin}/dashboard/subscriptions?canceled=true`;

    // Create the checkout session
    const checkoutSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      customer_email: session.user.email || '',
      metadata: {
        userId: session.user.id || '',
        tierId: tierId.toString(),
        subscriptionType,
        ...(merchantShopId && { merchantShopId }),
        ...(shopCustomerId && { shopCustomerId }),
      },
    });

    // Return the checkout URL
    return NextResponse.json({ url: checkoutSession.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
