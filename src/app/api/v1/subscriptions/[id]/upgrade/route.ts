import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the subscription ID from the URL
    const subscriptionId = params.id;
    
    // Get the request body
    const body = await request.json();
    const { new_tier_id, prorate } = body;
    
    // Validate required fields
    if (!new_tier_id) {
      return NextResponse.json(
        { error: 'New tier ID is required' },
        { status: 400 }
      );
    }
    
    // Get the token from the request
    const token = await getToken({ req: request });
    
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Get the access token
    const accessToken = token.accessToken as string;
    
    if (!accessToken) {
      return NextResponse.json(
        { error: 'Access token not found' },
        { status: 401 }
      );
    }
    
    // Call the backend API to upgrade the subscription
    const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL ;
    const response = await fetch(`${apiUrl}/api/v1/subscriptions/${subscriptionId}/upgrade`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        new_tier_id: new_tier_id,
        prorate: prorate !== undefined ? prorate : true
      })
    });
    
    // Get the response data
    const data = await response.json();
    
    // If the response is not OK, return an error
    if (!response.ok) {
      return NextResponse.json(
        { error: data.error || 'Failed to upgrade subscription' },
        { status: response.status }
      );
    }
    
    // Return the response data
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error upgrading subscription:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
