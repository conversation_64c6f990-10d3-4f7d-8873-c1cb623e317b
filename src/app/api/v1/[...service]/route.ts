import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';

/**
 * Proxy API route that forwards all requests to the backend API
 * This route handles all HTTP methods and preserves the path structure
 */

// Get backend URL from environment
const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8080';

/**
 * Helper function to get authentication token
 */
async function getAuthToken(request: NextRequest): Promise<string | null> {
  // First try to get token from Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Try to get token from session
  try {
    const session = await getServerSession(authOptions);
    if (session?.accessToken) {
      return session.accessToken as string;
    }
    if ((session?.user as any)?.accessToken) {
      return (session.user as any).accessToken;
    }
  } catch (error) {
    console.warn('Failed to get session:', error);
  }

  return null;
}

/**
 * Helper function to forward request to backend
 */
async function forwardRequest(
  request: NextRequest,
  method: string,
  servicePath: string[]
): Promise<NextResponse> {
  try {
    // Construct the backend URL
    const backendPath = `/api/v1/${servicePath.join('/')}`;
    const url = new URL(backendPath, BACKEND_URL);
    
    // Preserve query parameters
    const searchParams = request.nextUrl.searchParams;
    searchParams.forEach((value, key) => {
      url.searchParams.append(key, value);
    });

    console.log(`Proxying ${method} request to: ${url.toString()}`);

    // Get authentication token
    const token = await getAuthToken(request);

    // Prepare headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add authentication if available
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
      console.log(`Using auth token: ${token.substring(0, 20)}...`);
    }

    // Forward other relevant headers
    const forwardHeaders = [
      'user-agent',
      'accept',
      'accept-language',
      'x-forwarded-for',
      'x-real-ip',
    ];

    forwardHeaders.forEach(headerName => {
      const headerValue = request.headers.get(headerName);
      if (headerValue) {
        headers[headerName] = headerValue;
      }
    });

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
    };

    // Add body for methods that support it
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      try {
        const body = await request.text();
        if (body) {
          requestOptions.body = body;
        }
      } catch (error) {
        console.warn('Failed to read request body:', error);
      }
    }

    // Make the request to backend
    const response = await fetch(url.toString(), requestOptions);

    console.log(`Backend response status: ${response.status}`);

    // Get response body
    const responseText = await response.text();
    let responseData;

    // Try to parse as JSON, fallback to text
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }

    // Create response with same status and headers
    const nextResponse = NextResponse.json(responseData, {
      status: response.status,
      statusText: response.statusText,
    });

    // Forward relevant response headers
    const responseHeaders = [
      'content-type',
      'cache-control',
      'etag',
      'last-modified',
    ];

    responseHeaders.forEach(headerName => {
      const headerValue = response.headers.get(headerName);
      if (headerValue) {
        nextResponse.headers.set(headerName, headerValue);
      }
    });

    // Add CORS headers
    nextResponse.headers.set('Access-Control-Allow-Origin', '*');
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    return nextResponse;

  } catch (error) {
    console.error('Proxy error:', error);
    
    return NextResponse.json(
      { 
        error: 'Proxy request failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// HTTP method handlers
export async function GET(
  request: NextRequest,
  { params }: { params: { service: string[] } }
) {
  return forwardRequest(request, 'GET', params.service);
}

export async function POST(
  request: NextRequest,
  { params }: { params: { service: string[] } }
) {
  return forwardRequest(request, 'POST', params.service);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { service: string[] } }
) {
  return forwardRequest(request, 'PUT', params.service);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { service: string[] } }
) {
  return forwardRequest(request, 'DELETE', params.service);
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { service: string[] } }
) {
  return forwardRequest(request, 'PATCH', params.service);
}

export async function OPTIONS(
  request: NextRequest,
  { params }: { params: { service: string[] } }
) {
  // Handle CORS preflight requests
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400',
    },
  });
}
