import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    // Get the request body as raw text
    const body = await req.text();

    // Get the Stripe signature header
    const signature = req.headers.get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing Stripe signature' },
        { status: 400 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL ;

    console.log(`Forwarding webhook to ${apiUrl}/webhook/stripe`);

    // Forward the webhook to the backend API
    const response = await fetch(`${apiUrl}/webhook/stripe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': signature,
      },
      body,
    });

    // Check if the response is OK
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);
      return NextResponse.json(
        { error: `Backend API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    // Try to parse the response as JSON
    let data;
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json();
      } catch (e) {
        console.error('Error parsing JSON response:', e);
        const text = await response.text();
        console.error('Response text:', text);
        data = { received: true };
      }
    } else {
      // Handle non-JSON response
      const text = await response.text();
      console.log('Non-JSON response:', text);
      data = { received: true };
    }

    // Return the response
    return NextResponse.json(
      data || { received: true },
      { status: response.status }
    );
  } catch (error) {
    console.error('Error processing Stripe webhook:', error);
    return NextResponse.json(
      { error: 'Failed to process webhook' },
      { status: 500 }
    );
  }
}
