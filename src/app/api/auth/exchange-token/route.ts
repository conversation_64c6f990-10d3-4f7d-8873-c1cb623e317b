import { getToken } from "next-auth/jwt";
import { NextRequest, NextResponse } from "next/server";

/**
 * This API route exchanges a Google OAuth token for a backend JWT token
 * It should be called after successful Google authentication
 */
export async function GET(request: NextRequest) {
  try {
    console.log("Exchange token API route called");

    // Get the token from NextAuth
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    console.log("Token from NextAuth:", token);

    if (!token || !token.accessToken) {
      console.error("No access token found in token object:", token);
      return NextResponse.json(
        { error: "No access token found" },
        { status: 401 }
      );
    }

    const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL ;
    console.log(`Calling backend API at ${apiUrl}/api/v1/auth/google`);

    // Exchange the Google token for a backend JWT token
    const response = await fetch(
      `${apiUrl}/api/v1/auth/google`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token: token.accessToken }),
      }
    );

    console.log("Backend response status:", response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Error response from backend:", errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch (e) {
        errorData = { error: errorText };
      }

      return NextResponse.json(
        { error: errorData.error || "Failed to exchange token" },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log("Token exchange successful, received data:", data);

    // Return the token for the client to store
    return NextResponse.json({
      token: data.token,
      refreshToken: data.refresh_token,
      user: data.user,
    });
  } catch (error) {
    console.error("Error exchanging token:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to exchange token" },
      { status: 500 }
    );
  }
}
