import { NextRequest, NextResponse } from "next/server";

/**
 * This API route tests a JWT token against the backend API
 */
export async function POST(request: NextRequest) {
  try {
    console.log("Test token API route called");
    
    // Get the token from the request body
    const body = await request.json();
    const { token } = body;
    
    if (!token) {
      return NextResponse.json(
        { error: "No token provided" },
        { status: 400 }
      );
    }
    
    console.log(`Testing token: ${token.substring(0, 20)}...`);
    
    // Test the token against various endpoints
    const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL ;
    const endpoints = [
      '/api/v1/organizations',
      '/api/v1/users/me',
      '/api/v1/credits'
    ];
    
    const results = await Promise.all(
      endpoints.map(async (endpoint) => {
        try {
          console.log(`Testing endpoint: ${endpoint}`);
          
          const response = await fetch(`${apiUrl}${endpoint}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          });
          
          const status = response.status;
          let data;
          
          try {
            const text = await response.text();
            try {
              data = JSON.parse(text);
            } catch (e) {
              data = { text };
            }
          } catch (e) {
            data = { error: "Failed to read response" };
          }
          
          return {
            endpoint,
            success: response.ok,
            status,
            data: response.ok ? data : (data.error || "Unknown error")
          };
        } catch (error) {
          return {
            endpoint,
            success: false,
            status: 0,
            data: error instanceof Error ? error.message : "Unknown error"
          };
        }
      })
    );
    
    return NextResponse.json({
      token_preview: token.substring(0, 20) + '...',
      results
    });
  } catch (error) {
    console.error("Error testing token:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to test token" },
      { status: 500 }
    );
  }
}
