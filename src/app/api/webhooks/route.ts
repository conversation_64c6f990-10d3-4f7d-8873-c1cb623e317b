import { NextResponse } from "next/server";
import { headers } from "next/headers";
import <PERSON><PERSON> from "stripe";

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "", {
  apiVersion: "2025-04-30.basil",
});

// Map Stripe product IDs to subscription tier IDs
const productToTierMap: Record<string, number> = {
  "prod_SKliZSGRg7VYIp": 1, // Free Tier
  "prod_SKlj9ZjpRtaIXP": 2, // Pro Tier
  "prod_SKljlTQvPzMrss": 3, // Enterprise Tier
};

export async function POST(req: Request) {
  const body = await req.text();
  const signature = headers().get("stripe-signature") || "";

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET || ""
    );
  } catch (error: any) {
    console.error(`Webhook signature verification failed: ${error.message}`);
    return NextResponse.json({ error: error.message }, { status: 400 });
  }

  // Handle the event
  switch (event.type) {
    case "checkout.session.completed": {
      const session = event.data.object as Stripe.Checkout.Session;

      // Retrieve the subscription details
      const subscription = await stripe.subscriptions.retrieve(
        session.subscription as string
      );

      // Get the product ID from the subscription
      const productId = subscription.items.data[0].price.product as string;

      // Get the subscription tier ID from the product ID
      const subscriptionTierId = productToTierMap[productId];

      if (!subscriptionTierId) {
        console.error(`Unknown product ID: ${productId}`);
        return NextResponse.json(
          { error: "Unknown product ID" },
          { status: 400 }
        );
      }

      // Get user ID from the session metadata
      const userId = session.metadata?.userId;

      if (!userId) {
        console.error("No user ID found in session metadata");
        return NextResponse.json(
          { error: "No user ID found in session metadata" },
          { status: 400 }
        );
      }

      try {
        // Get user information from session metadata
        const userId = session.metadata?.userId;
        const productName = session.metadata?.productName;

        if (!userId) {
          throw new Error('User ID not found in session metadata');
        }

        // Make a direct call to the backend API to create a subscription
        const apiUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
        const subscriptionsEndpoint = apiUrl.endsWith('/api/v1')
          ? `${apiUrl}/subscriptions`
          : `${apiUrl}/api/v1/subscriptions`;

        console.log(`Creating subscription for user ${userId} with tier ${subscriptionTierId}`);

        // For testing purposes, we'll use a hardcoded token
        // In a real app, you would have a more secure way to get the token
        // This token should be a valid JWT token for the user
        const userToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************.Yx_FhPPLbQ5yJZ8tMJvWicV7AwjR4B-cqD5OGlq9Lxs";

        // Make the API call to create the subscription
        const response = await fetch(subscriptionsEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${userToken}`
          },
          body: JSON.stringify({
            user_id: userId,
            subscription_tier_id: subscriptionTierId,
            auto_renew: true
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create subscription in backend');
        }

        const result = await response.json();
        console.log(`Subscription created for user ${userId} with tier ${subscriptionTierId}`, result);
      } catch (error) {
        console.error("Error creating subscription:", error);
        return NextResponse.json(
          { error: "Failed to create subscription" },
          { status: 500 }
        );
      }

      break;
    }

    case "invoice.payment_succeeded": {
      // Handle successful recurring payments
      const invoice = event.data.object as Stripe.Invoice;
      console.log(`Payment succeeded for invoice ${invoice.id}`);
      break;
    }

    case "customer.subscription.deleted": {
      // Handle subscription cancellations
      const subscription = event.data.object as Stripe.Subscription;
      console.log(`Subscription ${subscription.id} was canceled`);
      break;
    }

    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  return NextResponse.json({ received: true });
}
