'use client';

import { useState } from 'react';
import { proxyApi, proxyExamples } from '@/lib/api/proxy-client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

export default function TestProxyPage() {
  const [response, setResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Custom request state
  const [customEndpoint, setCustomEndpoint] = useState('/users/me');
  const [customMethod, setCustomMethod] = useState('GET');
  const [customBody, setCustomBody] = useState('');

  const handleRequest = async (requestFn: () => Promise<any>, description: string) => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      console.log(`Testing: ${description}`);
      const result = await requestFn();
      setResponse(result);
      console.log(`Success: ${description}`, result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error(`Error: ${description}`, err);
    } finally {
      setLoading(false);
    }
  };

  const handleCustomRequest = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      let result;
      const body = customBody.trim() ? JSON.parse(customBody) : undefined;

      switch (customMethod) {
        case 'GET':
          result = await proxyApi.get(customEndpoint);
          break;
        case 'POST':
          result = await proxyApi.post(customEndpoint, body);
          break;
        case 'PUT':
          result = await proxyApi.put(customEndpoint, body);
          break;
        case 'DELETE':
          result = await proxyApi.delete(customEndpoint);
          break;
        case 'PATCH':
          result = await proxyApi.patch(customEndpoint, body);
          break;
        default:
          throw new Error('Unsupported method');
      }

      setResponse(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Proxy API Test Page</h1>
        <p className="text-muted-foreground mt-2">
          Test the Next.js proxy API route that forwards requests to the backend
        </p>
      </div>

      {/* Quick Test Buttons */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Tests</CardTitle>
          <CardDescription>
            Test common API endpoints through the proxy
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Button
              onClick={() => handleRequest(proxyExamples.getCurrentUser, 'Get Current User')}
              disabled={loading}
              variant="outline"
            >
              Get Current User
            </Button>
            
            <Button
              onClick={() => handleRequest(proxyExamples.getOrganizations, 'Get Organizations')}
              disabled={loading}
              variant="outline"
            >
              Get Organizations
            </Button>
            
            <Button
              onClick={() => handleRequest(proxyExamples.getApiKeys, 'Get API Keys')}
              disabled={loading}
              variant="outline"
            >
              Get API Keys
            </Button>
            
            <Button
              onClick={() => handleRequest(proxyExamples.getMerchantShops, 'Get Merchant Shops')}
              disabled={loading}
              variant="outline"
            >
              Get Merchant Shops
            </Button>
            
            <Button
              onClick={() => handleRequest(proxyExamples.getCredits, 'Get Credits')}
              disabled={loading}
              variant="outline"
            >
              Get Credits
            </Button>
            
            <Button
              onClick={() => handleRequest(proxyExamples.getSubscriptions, 'Get Subscriptions')}
              disabled={loading}
              variant="outline"
            >
              Get Subscriptions
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Custom Request */}
      <Card>
        <CardHeader>
          <CardTitle>Custom Request</CardTitle>
          <CardDescription>
            Make a custom request through the proxy
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="endpoint">Endpoint</Label>
              <Input
                id="endpoint"
                value={customEndpoint}
                onChange={(e) => setCustomEndpoint(e.target.value)}
                placeholder="/users/me"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="method">Method</Label>
              <select
                id="method"
                value={customMethod}
                onChange={(e) => setCustomMethod(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
                <option value="PATCH">PATCH</option>
              </select>
            </div>
          </div>
          
          {['POST', 'PUT', 'PATCH'].includes(customMethod) && (
            <div className="space-y-2">
              <Label htmlFor="body">Request Body (JSON)</Label>
              <Textarea
                id="body"
                value={customBody}
                onChange={(e) => setCustomBody(e.target.value)}
                placeholder='{"key": "value"}'
                rows={4}
              />
            </div>
          )}
          
          <Button
            onClick={handleCustomRequest}
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Sending...' : 'Send Request'}
          </Button>
        </CardContent>
      </Card>

      {/* Response Display */}
      {(response || error) && (
        <Card>
          <CardHeader>
            <CardTitle className={error ? 'text-red-600' : 'text-green-600'}>
              {error ? 'Error Response' : 'Success Response'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-sm">
              {error ? error : JSON.stringify(response, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
