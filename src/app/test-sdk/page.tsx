"use client";

import { useState, useEffect } from 'react';
import ADCCreditSDK from '@/sdk';
import { APIKey, Webhook, Transaction } from '@/types';
import { getStoredToken } from '@/lib/auth/token-utils';

export default function TestSDKPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [creditBalance, setCreditBalance] = useState<number | null>(null);
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [webhooks, setWebhooks] = useState<Webhook[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [organizations, setOrganizations] = useState<any[]>([]);
  const [testResults, setTestResults] = useState<{
    method: string;
    success: boolean;
    message: string;
  }[]>([]);

  useEffect(() => {
    const runTests = async () => {
      setLoading(true);
      setError(null);

      try {
        // Get the stored token
        const token = getStoredToken();
        if (!token) {
          setError('No authentication token found. Please log in first.');
          setLoading(false);
          return;
        }

        // Initialize the SDK with the token
        const sdk = new ADCCreditSDK({
          apiUrl: process.env.NEXT_PUBLIC_BACKEND_URL,
          token: token,
          debug: true
        });

        // Add test result
        const addResult = (method: string, success: boolean, message: string) => {
          setTestResults(prev => [...prev, { method, success, message }]);
        };

        // Test 1: Get credit balance
        try {
          const balanceResponse = await sdk.credits.getBalance();
          if (balanceResponse.error) {
            addResult('getBalance', false, `Error: ${balanceResponse.error}`);
          } else {
            setCreditBalance(balanceResponse.data.credit_balance);
            addResult('getBalance', true, `Credit balance: ${balanceResponse.data.credit_balance}`);
          }
        } catch (e) {
          addResult('getBalance', false, `Exception: ${e instanceof Error ? e.message : String(e)}`);
        }

        // Test 2: Get API keys
        try {
          const apiKeysResponse = await sdk.apiKeys.getAll();
          if (apiKeysResponse.error) {
            addResult('getApiKeys', false, `Error: ${apiKeysResponse.error}`);
          } else {
            setApiKeys(apiKeysResponse.data);
            addResult('getApiKeys', true, `Found ${apiKeysResponse.data.length} API keys`);
          }
        } catch (e) {
          addResult('getApiKeys', false, `Exception: ${e instanceof Error ? e.message : String(e)}`);
        }

        // Test 3: Get webhooks
        try {
          const webhooksResponse = await sdk.webhooks.getAll();
          if (webhooksResponse.error) {
            addResult('getWebhooks', false, `Error: ${webhooksResponse.error}`);
          } else {
            setWebhooks(webhooksResponse.data);
            addResult('getWebhooks', true, `Found ${webhooksResponse.data.length} webhooks`);
          }
        } catch (e) {
          addResult('getWebhooks', false, `Exception: ${e instanceof Error ? e.message : String(e)}`);
        }

        // Test 4: Get transactions
        try {
          const transactionsResponse = await sdk.credits.getTransactions();
          if (transactionsResponse.error) {
            addResult('getTransactions', false, `Error: ${transactionsResponse.error}`);
          } else {
            setTransactions(transactionsResponse.data);
            addResult('getTransactions', true, `Found ${transactionsResponse.data.length} transactions`);
          }
        } catch (e) {
          addResult('getTransactions', false, `Exception: ${e instanceof Error ? e.message : String(e)}`);
        }

        // Test 5: Get current user
        try {
          const userResponse = await sdk.users.getCurrentUser();
          if (userResponse.error) {
            addResult('getCurrentUser', false, `Error: ${userResponse.error}`);
          } else {
            setCurrentUser(userResponse.data);
            addResult('getCurrentUser', true, `User: ${userResponse.data.name} (${userResponse.data.email})`);
          }
        } catch (e) {
          addResult('getCurrentUser', false, `Exception: ${e instanceof Error ? e.message : String(e)}`);
        }

        // Test 6: Get organizations
        try {
          const orgsResponse = await sdk.organizations.getAll();
          if (orgsResponse.error) {
            addResult('getOrganizations', false, `Error: ${orgsResponse.error}`);
          } else {
            setOrganizations(orgsResponse.data);
            addResult('getOrganizations', true, `Found ${orgsResponse.data.length} organizations`);
          }
        } catch (e) {
          addResult('getOrganizations', false, `Exception: ${e instanceof Error ? e.message : String(e)}`);
        }

      } catch (e) {
        setError(`Failed to initialize SDK: ${e instanceof Error ? e.message : String(e)}`);
      } finally {
        setLoading(false);
      }
    };

    runTests();
  }, []);

  return (
    <div className="space-y-6 container mx-auto ">
      <h1 className="text-2xl font-bold">SDK Test Page</h1>

      {loading && <div className="text-blue-500">Loading...</div>}

      {error && (
        <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {!loading && !error && (
        <>
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Test Results</h2>
            <div className="border rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {testResults.map((result, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{result.method}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {result.success ? 'Success' : 'Failed'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{result.message}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {creditBalance !== null && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h2 className="text-lg font-semibold text-green-800">Credit Balance: {creditBalance}</h2>
            </div>
          )}

          {apiKeys.length > 0 && (
            <div className="space-y-2">
              <h2 className="text-xl font-semibold">API Keys ({apiKeys.length})</h2>
              <div className="border rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {apiKeys.map((key) => (
                      <tr key={key.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{key.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${key.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {key.enabled ? 'Enabled' : 'Disabled'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(key.created_at).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {currentUser && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mt-4">
              <h2 className="text-lg font-semibold text-blue-800">Current User</h2>
              <div className="mt-2">
                <p><strong>Name:</strong> {currentUser.name}</p>
                <p><strong>Email:</strong> {currentUser.email}</p>
                <p><strong>Role:</strong> {currentUser.role}</p>
              </div>
            </div>
          )}

          {organizations.length > 0 && (
            <div className="space-y-2 mt-4">
              <h2 className="text-xl font-semibold">Organizations ({organizations.length})</h2>
              <div className="border rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Slug</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {organizations.map((org) => (
                      <tr key={org.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{org.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{org.slug}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(org.created_at).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
