"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PageHeader } from "@/components/layout/page-header";
import {
  Building2,
  Plus,
  RefreshCw,
  Edit,
  Trash2,
  Users,
  FolderTree
} from "lucide-react";
import {
  useGetOrganizationsQuery,
  useCreateOrganizationMutation,
  useDeleteOrganizationMutation
} from "@/lib/api/apiSlice";
import { Organization } from "@/types";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import Link from "next/link";

export default function OrganizationsPage() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isLoading, setLoading] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newOrgName, setNewOrgName] = useState("");
  const [newOrgDescription, setNewOrgDescription] = useState("");

  // Use RTK Query hooks to fetch data
  const {
    data: organizations = [],
    isLoading: isLoadingOrgs,
    refetch: refetchOrgs
  } = useGetOrganizationsQuery(undefined);

  const [createOrganization, { isLoading: isCreating }] = useCreateOrganizationMutation();
  const [deleteOrganization, { isLoading: isDeleting }] = useDeleteOrganizationMutation();

  // Function to refresh all data
  const refreshData = () => {
    setLoading(true);
    refetchOrgs();
    setTimeout(() => setLoading(false), 500);
  };

  // Function to handle organization creation
  const handleCreateOrganization = async () => {
    if (!newOrgName) {
      toast({
        title: "Error",
        description: "Organization name is required",
        variant: "destructive"
      });
      return;
    }

    try {
      await createOrganization({
        name: newOrgName,
        description: newOrgDescription
      }).unwrap();

      toast({
        title: "Success",
        description: "Organization created successfully"
      });

      // Reset form and close dialog
      setNewOrgName("");
      setNewOrgDescription("");
      setIsCreateDialogOpen(false);

      // Refresh data
      refetchOrgs();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create organization",
        variant: "destructive"
      });
    }
  };

  // Function to handle organization deletion
  const handleDeleteOrganization = async (id: string) => {
    if (!confirm("Are you sure you want to delete this organization? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteOrganization(id).unwrap();

      toast({
        title: "Success",
        description: "Organization deleted successfully"
      });

      // Refresh data
      refetchOrgs();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete organization",
        variant: "destructive"
      });
    }
  };

  if (isLoadingOrgs || isLoading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <PageHeader
        heading="Organizations"
        description="Manage your organizations and their branches"
      >
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={refreshData}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="gap-1">
                <Plus className="h-4 w-4" />
                New Organization
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Organization</DialogTitle>
                <DialogDescription>
                  Create a new organization to manage branches and external users.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    placeholder="Organization name"
                    value={newOrgName}
                    onChange={(e) => setNewOrgName(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Organization description"
                    value={newOrgDescription}
                    onChange={(e) => setNewOrgDescription(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateOrganization} disabled={isCreating}>
                  {isCreating ? 'Creating...' : 'Create'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </PageHeader>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-6">
        {organizations.length > 0 ? (
          organizations.map((org: Organization) => (
            <Card key={org.id}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-medium flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-primary" />
                    {org.name}
                  </CardTitle>
                </div>
                <CardDescription>{org.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-sm mb-2">
                  <span>Branches:</span>
                  <Badge variant="outline">{org.branches?.length || 0}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Created:</span>
                  <span className="text-muted-foreground">
                    {new Date(org.created_at).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
              <CardFooter className="flex gap-2">
                <Link href={`/dashboard/organizations/${org.slug}`} className="flex-1">
                  <Button variant="outline" size="sm" className="w-full gap-1">
                    <FolderTree className="h-4 w-4" />
                    Manage
                  </Button>
                </Link>
                <Button
                  variant="destructive"
                  size="sm"
                  className="gap-1"
                  onClick={() => handleDeleteOrganization(org.slug)}
                  disabled={isDeleting}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center p-8 text-center">
            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Organizations</h3>
            <p className="text-muted-foreground mb-4">
              You haven't created any organizations yet. Create your first organization to get started.
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Organization
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
