"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { PageHeader } from "@/components/layout/page-header";
import { use } from "react";
import {
  Building2,
  Plus,
  RefreshCw,
  Edit,
  Trash2,
  Users,
  FolderTree,
  ArrowLeft
} from "lucide-react";
import {
  useGetOrganizationQuery,
  useUpdateOrganizationMutation,
  useGetBranchesQuery,
  useCreateBranchMutation,
  useDeleteBranchMutation
} from "@/lib/api/apiSlice";
import { Organization, Branch } from "@/types";
import { Dialog, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function OrganizationDetailPage({ params }: { params: { slug: string } }) {
  // Unwrap params using React.use() as recommended by Next.js
  const unwrappedParams = use(params as any) as { slug: string };
  const { slug } = unwrappedParams;
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isLoading, setLoading] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isCreateBranchDialogOpen, setIsCreateBranchDialogOpen] = useState(false);
  const [orgName, setOrgName] = useState("");
  const [orgDescription, setOrgDescription] = useState("");
  const [newBranchName, setNewBranchName] = useState("");
  const [newBranchDescription, setNewBranchDescription] = useState("");

  // Use RTK Query hooks to fetch data
  const {
    data: organization,
    isLoading: isLoadingOrg,
    refetch: refetchOrg
  } = useGetOrganizationQuery(slug);

  const {
    data: branches = [],
    isLoading: isLoadingBranches,
    refetch: refetchBranches
  } = useGetBranchesQuery(organization?.id || '');

  const [updateOrganization, { isLoading: isUpdating }] = useUpdateOrganizationMutation();
  const [createBranch, { isLoading: isCreatingBranch }] = useCreateBranchMutation();
  const [deleteBranch, { isLoading: isDeletingBranch }] = useDeleteBranchMutation();

  // Set form values when organization data is loaded
  useState(() => {
    if (organization) {
      setOrgName(organization.name);
      setOrgDescription(organization.description);
    }
  });

  // Function to refresh all data
  const refreshData = () => {
    setLoading(true);
    refetchOrg();
    refetchBranches();
    setTimeout(() => setLoading(false), 500);
  };

  // Function to handle organization update
  const handleUpdateOrganization = async () => {
    if (!orgName) {
      toast({
        title: "Error",
        description: "Organization name is required",
        variant: "destructive"
      });
      return;
    }

    try {
      await updateOrganization({
        slug,
        name: orgName,
        description: orgDescription
      }).unwrap();

      toast({
        title: "Success",
        description: "Organization updated successfully"
      });

      // Close dialog
      setIsEditDialogOpen(false);

      // Refresh data
      refetchOrg();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update organization",
        variant: "destructive"
      });
    }
  };

  // Function to handle branch creation
  const handleCreateBranch = async () => {
    if (!newBranchName || !organization?.id) {
      toast({
        title: "Error",
        description: "Branch name is required",
        variant: "destructive"
      });
      return;
    }

    try {
      await createBranch({
        organizationId: organization.id,
        name: newBranchName,
        description: newBranchDescription
      }).unwrap();

      toast({
        title: "Success",
        description: "Branch created successfully"
      });

      // Reset form and close dialog
      setNewBranchName("");
      setNewBranchDescription("");
      setIsCreateBranchDialogOpen(false);

      // Refresh data
      refetchBranches();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create branch",
        variant: "destructive"
      });
    }
  };

  // Function to handle branch deletion
  const handleDeleteBranch = async (branchId: string) => {
    if (!confirm("Are you sure you want to delete this branch? This action cannot be undone.") || !organization?.id) {
      return;
    }

    try {
      await deleteBranch({ id: branchId, organizationId: organization.id }).unwrap();

      toast({
        title: "Success",
        description: "Branch deleted successfully"
      });

      // Refresh data
      refetchBranches();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete branch",
        variant: "destructive"
      });
    }
  };

  if (isLoadingOrg || isLoadingBranches || isLoading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center flex-col">
        <h2 className="text-xl font-semibold mb-4">Organization not found</h2>
        <Button onClick={() => router.push('/dashboard/organizations')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Organizations
        </Button>
      </div>
    );
  }

  return (
    <div>
      <PageHeader
        heading={organization.name}
        description={organization.description}
      >
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={() => router.push('/dashboard/organizations')}
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={refreshData}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </Button>
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="gap-1">
                <Edit className="h-4 w-4" />
                Edit
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Organization</DialogTitle>
                <DialogDescription>
                  Update organization details.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    placeholder="Organization name"
                    value={orgName}
                    onChange={(e) => setOrgName(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Organization description"
                    value={orgDescription}
                    onChange={(e) => setOrgDescription(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleUpdateOrganization} disabled={isUpdating}>
                  {isUpdating ? 'Updating...' : 'Update'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </PageHeader>

      <div className="mt-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Branches</h2>
          <Dialog open={isCreateBranchDialogOpen} onOpenChange={setIsCreateBranchDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="gap-1">
                <Plus className="h-4 w-4" />
                New Branch
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Branch</DialogTitle>
                <DialogDescription>
                  Create a new branch for this organization.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    placeholder="Branch name"
                    value={newBranchName}
                    onChange={(e) => setNewBranchName(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Branch description"
                    value={newBranchDescription}
                    onChange={(e) => setNewBranchDescription(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateBranchDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateBranch} disabled={isCreatingBranch}>
                  {isCreatingBranch ? 'Creating...' : 'Create'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {branches.length > 0 ? (
            branches.map((branch: Branch) => (
              <Card key={branch.id}>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base font-medium flex items-center gap-2">
                      <FolderTree className="h-4 w-4 text-primary" />
                      {branch.name}
                    </CardTitle>
                  </div>
                  <CardDescription>{branch.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span>Users:</span>
                    <Badge variant="outline">{branch.users?.length || 0}</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Created:</span>
                    <span className="text-muted-foreground">
                      {new Date(branch.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </CardContent>
                <CardFooter className="flex gap-2">
                  <Link href={`/dashboard/organizations/${slug}/branches/${branch.id}`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full gap-1">
                      <Users className="h-4 w-4" />
                      Manage Users
                    </Button>
                  </Link>
                  <Button
                    variant="destructive"
                    size="sm"
                    className="gap-1"
                    onClick={() => handleDeleteBranch(branch.id)}
                    disabled={isDeletingBranch}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            ))
          ) : (
            <div className="col-span-full flex flex-col items-center justify-center p-8 text-center">
              <FolderTree className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Branches</h3>
              <p className="text-muted-foreground mb-4">
                This organization doesn't have any branches yet. Create your first branch to get started.
              </p>
              <Button onClick={() => setIsCreateBranchDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Branch
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
