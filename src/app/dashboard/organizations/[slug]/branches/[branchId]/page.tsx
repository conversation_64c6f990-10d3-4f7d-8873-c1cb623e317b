"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { PageHeader } from "@/components/layout/page-header";
import {
  Building2,
  Plus,
  RefreshCw,
  Edit,
  Trash2,
  Users,
  FolderTree,
  ArrowLeft,
  User,
  CreditCard,
  PlusCircle,
  MinusCircle
} from "lucide-react";
import {
  useGetOrganizationQuery,
  useGetBranchQuery,
  useGetExternalUsersQuery,
  useCreateExternalUserMutation,
  useUpdateExternalUserMutation,
  useDeleteExternalUserMutation,
  useAddCreditsToExternalUserMutation,
  useReduceCreditsFromExternalUserMutation
} from "@/lib/api/apiSlice";
import { Organization, Branch, ExternalUser } from "@/types";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Progress } from "@/components/ui/progress";

export default function BranchDetailPage({ params }: { params: { slug: string, branchId: string } }) {
  const { slug: orgSlug, branchId } = params;
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isLoading, setLoading] = useState(false);
  const [isCreateUserDialogOpen, setIsCreateUserDialogOpen] = useState(false);
  const [isAddCreditsDialogOpen, setIsAddCreditsDialogOpen] = useState(false);
  const [isReduceCreditsDialogOpen, setIsReduceCreditsDialogOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState("");
  const [selectedUserName, setSelectedUserName] = useState("");

  // Form states
  const [newUserName, setNewUserName] = useState("");
  const [newUserEmail, setNewUserEmail] = useState("");
  const [newUserIdentifier, setNewUserIdentifier] = useState("");
  const [newUserCredits, setNewUserCredits] = useState(100);
  const [creditAmount, setCreditAmount] = useState(0);
  const [creditDescription, setCreditDescription] = useState("");

  // Use RTK Query hooks to fetch data
  const {
    data: organization,
    isLoading: isLoadingOrg
  } = useGetOrganizationQuery(orgSlug);

  const {
    data: branch,
    isLoading: isLoadingBranch
  } = useGetBranchQuery({ id: branchId, organizationId: organization?.id || '' });

  const {
    data: externalUsers = [],
    isLoading: isLoadingUsers,
    refetch: refetchUsers
  } = useGetExternalUsersQuery({ organizationId: organization?.id || '', branchId });

  const [createExternalUser, { isLoading: isCreatingUser }] = useCreateExternalUserMutation();
  const [updateExternalUser, { isLoading: isUpdatingUser }] = useUpdateExternalUserMutation();
  const [deleteExternalUser, { isLoading: isDeletingUser }] = useDeleteExternalUserMutation();
  const [addCredits, { isLoading: isAddingCredits }] = useAddCreditsToExternalUserMutation();
  const [reduceCredits, { isLoading: isReducingCredits }] = useReduceCreditsFromExternalUserMutation();

  // Function to refresh all data
  const refreshData = () => {
    setLoading(true);
    refetchUsers();
    setTimeout(() => setLoading(false), 500);
  };

  // Function to handle external user creation
  const handleCreateExternalUser = async () => {
    if (!newUserName || !newUserEmail || !newUserIdentifier || !organization?.id) {
      toast({
        title: "Error",
        description: "All fields are required",
        variant: "destructive"
      });
      return;
    }

    try {
      await createExternalUser({
        organizationId: organization.id,
        branchId,
        name: newUserName,
        email: newUserEmail,
        external_user_identifier: newUserIdentifier,
        monthly_credits: newUserCredits
      }).unwrap();

      toast({
        title: "Success",
        description: "External user created successfully"
      });

      // Reset form and close dialog
      setNewUserName("");
      setNewUserEmail("");
      setNewUserIdentifier("");
      setNewUserCredits(100);
      setIsCreateUserDialogOpen(false);

      // Refresh data
      refetchUsers();
    } catch (error: any) {
      // Check if the error has a data property with an error message
      const errorMessage = error?.data?.error || "Failed to create external user";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });

      console.error("Create external user error:", error);
    }
  };

  // Function to handle external user deletion
  const handleDeleteExternalUser = async (userId: string) => {
    if (!confirm("Are you sure you want to delete this user? This action cannot be undone.") || !organization?.id) {
      return;
    }

    try {
      await deleteExternalUser({ id: userId, organizationId: organization.id, branchId }).unwrap();

      toast({
        title: "Success",
        description: "External user deleted successfully"
      });

      // Refresh data
      refetchUsers();
    } catch (error: any) {
      const errorMessage = error?.data?.error || "Failed to delete external user";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });

      console.error("Delete external user error:", error);
    }
  };

  // Function to handle adding credits
  const handleAddCredits = async () => {
    if (creditAmount <= 0 || !organization?.id) {
      toast({
        title: "Error",
        description: "Credit amount must be greater than 0",
        variant: "destructive"
      });
      return;
    }

    try {
      await addCredits({
        id: selectedUserId,
        organizationId: organization.id,
        branchId,
        amount: creditAmount,
        description: creditDescription || "Manual credit addition"
      }).unwrap();

      toast({
        title: "Success",
        description: `Added ${creditAmount} credits to ${selectedUserName}`
      });

      // Reset form and close dialog
      setCreditAmount(0);
      setCreditDescription("");
      setIsAddCreditsDialogOpen(false);

      // Refresh data
      refetchUsers();
    } catch (error: any) {
      const errorMessage = error?.data?.error || "Failed to add credits";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });

      console.error("Add credits error:", error);
    }
  };

  // Function to handle reducing credits
  const handleReduceCredits = async () => {
    if (creditAmount <= 0 || !organization?.id) {
      toast({
        title: "Error",
        description: "Credit amount must be greater than 0",
        variant: "destructive"
      });
      return;
    }

    try {
      await reduceCredits({
        id: selectedUserId,
        organizationId: organization.id,
        branchId,
        amount: creditAmount,
        description: creditDescription || "Manual credit reduction"
      }).unwrap();

      toast({
        title: "Success",
        description: `Reduced ${creditAmount} credits from ${selectedUserName}`
      });

      // Reset form and close dialog
      setCreditAmount(0);
      setCreditDescription("");
      setIsReduceCreditsDialogOpen(false);

      // Refresh data
      refetchUsers();
    } catch (error: any) {
      const errorMessage = error?.data?.error || "Failed to reduce credits";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });

      console.error("Reduce credits error:", error);
    }
  };

  if (isLoadingOrg || isLoadingBranch || isLoadingUsers || isLoading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!organization || !branch) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center flex-col">
        <h2 className="text-xl font-semibold mb-4">Branch not found</h2>
        <Button onClick={() => router.push(`/dashboard/organizations/${orgSlug}`)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Organization
        </Button>
      </div>
    );
  }

  return (
    <div>
      <PageHeader
        heading={branch.name}
        description={`${organization.name} > ${branch.description}`}
      >
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={() => router.push(`/dashboard/organizations/${orgSlug}`)}
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={refreshData}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </Button>
          <Dialog open={isCreateUserDialogOpen} onOpenChange={setIsCreateUserDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="gap-1">
                <Plus className="h-4 w-4" />
                New User
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create External User</DialogTitle>
                <DialogDescription>
                  Create a new external user for this branch.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    placeholder="User name"
                    value={newUserName}
                    onChange={(e) => setNewUserName(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="User email"
                    value={newUserEmail}
                    onChange={(e) => setNewUserEmail(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="identifier">External Identifier</Label>
                  <Input
                    id="identifier"
                    placeholder="Unique identifier in your system"
                    value={newUserIdentifier}
                    onChange={(e) => setNewUserIdentifier(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="credits">Monthly Credits</Label>
                  <Input
                    id="credits"
                    type="number"
                    placeholder="Monthly credit allocation"
                    value={newUserCredits}
                    onChange={(e) => setNewUserCredits(parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateUserDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateExternalUser} disabled={isCreatingUser}>
                  {isCreatingUser ? 'Creating...' : 'Create'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </PageHeader>

      <div className="mt-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">External Users</h2>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {externalUsers.length > 0 ? (
            externalUsers.map((user: ExternalUser) => (
              <Card key={user.id}>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base font-medium flex items-center gap-2">
                      <User className="h-4 w-4 text-primary" />
                      {user.name}
                    </CardTitle>
                    <Badge variant="outline" className="text-xs">
                      {user.external_user_identifier}
                    </Badge>
                  </div>
                  <CardDescription>{user.email}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">Credits</span>
                        <span className="text-sm text-muted-foreground">
                          {user.subscriptions?.[0]?.credit_balance || 0} / {user.monthly_credits}
                        </span>
                      </div>
                      <Progress
                        value={user.subscriptions?.[0]?.credit_balance ? (user.subscriptions[0].credit_balance / user.monthly_credits) * 100 : 0}
                        className="h-2"
                      />
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Next Reset:</span>
                      <span className="text-muted-foreground">
                        {new Date(user.next_credit_reset_date).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col gap-2">
                  <div className="flex gap-2 w-full">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 gap-1"
                      onClick={() => {
                        setSelectedUserId(user.id);
                        setSelectedUserName(user.name);
                        setIsAddCreditsDialogOpen(true);
                      }}
                    >
                      <PlusCircle className="h-4 w-4" />
                      Add Credits
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 gap-1"
                      onClick={() => {
                        setSelectedUserId(user.id);
                        setSelectedUserName(user.name);
                        setIsReduceCreditsDialogOpen(true);
                      }}
                    >
                      <MinusCircle className="h-4 w-4" />
                      Reduce
                    </Button>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    className="w-full gap-1"
                    onClick={() => handleDeleteExternalUser(user.id)}
                    disabled={isDeletingUser}
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete User
                  </Button>
                </CardFooter>
              </Card>
            ))
          ) : (
            <div className="col-span-full flex flex-col items-center justify-center p-8 text-center">
              <Users className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No External Users</h3>
              <p className="text-muted-foreground mb-4">
                This branch doesn't have any external users yet. Create your first user to get started.
              </p>
              <Button onClick={() => setIsCreateUserDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create External User
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Add Credits Dialog */}
      <Dialog open={isAddCreditsDialogOpen} onOpenChange={setIsAddCreditsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Credits</DialogTitle>
            <DialogDescription>
              Add credits to {selectedUserName}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                placeholder="Credit amount"
                value={creditAmount}
                onChange={(e) => setCreditAmount(parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description (optional)</Label>
              <Textarea
                id="description"
                placeholder="Reason for adding credits"
                value={creditDescription}
                onChange={(e) => setCreditDescription(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddCreditsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddCredits} disabled={isAddingCredits}>
              {isAddingCredits ? 'Adding...' : 'Add Credits'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reduce Credits Dialog */}
      <Dialog open={isReduceCreditsDialogOpen} onOpenChange={setIsReduceCreditsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reduce Credits</DialogTitle>
            <DialogDescription>
              Reduce credits from {selectedUserName}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                placeholder="Credit amount"
                value={creditAmount}
                onChange={(e) => setCreditAmount(parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description (optional)</Label>
              <Textarea
                id="description"
                placeholder="Reason for reducing credits"
                value={creditDescription}
                onChange={(e) => setCreditDescription(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReduceCreditsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleReduceCredits} disabled={isReducingCredits}>
              {isReducingCredits ? 'Reducing...' : 'Reduce Credits'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
