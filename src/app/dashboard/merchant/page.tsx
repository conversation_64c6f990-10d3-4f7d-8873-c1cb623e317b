"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Storefront, CurrencyDollar, User, House, CaretRight, QrCode, ChartBar, ShoppingBag, Gear } from "phosphor-react";
import Link from "next/link";
import { useGetMerchantShopsQuery, useGetMerchantCreditStatsQuery } from "@/lib/api/apiSlice";
import { MerchantShop } from "@/types";

export default function MerchantDashboardPage() {
  const { data: shops, isLoading: isShopsLoading } = useGetMerchantShopsQuery();
  const {
    data: creditStats,
    isLoading: isStatsLoading
  } = useGetMerchantCreditStatsQuery();
  const router = useRouter();

  // Get credit statistics from API
  const totalCreditsIssued = creditStats?.total_credits_issued || 0;
  const totalCreditsRedeemed = creditStats?.total_credits_redeemed || 0;

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div className="flex-1">
        {/* Header - Mobile & Desktop */}
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
          <h2 className="text-[#181510] text-lg md:text-2xl font-bold leading-tight tracking-[-0.015em] flex-1 text-center pl-12 md:pl-0">Merchant Dashboard</h2>
          <div className="flex w-12 items-center justify-end">
            <Link href="/dashboard/merchant/new">
              <Button
                className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 bg-transparent text-[#181510] gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0 md:bg-[#e5ccb2] md:px-4 md:py-2"
                variant="ghost"
              >
                <Plus size={24} weight="regular" className="text-[#181510]" />
                <span className="hidden md:inline">New Shop</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Main Content - Responsive Layout */}
        <div className="md:max-w-6xl md:mx-auto md:px-6 md:py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 md:p-0 md:mb-8">
            <Card className="bg-gradient-to-br from-[#e5ccb2] to-[#f1edea] border-none shadow-md overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510]">Total Credits Issued</CardTitle>
              </CardHeader>
              <CardContent>
                {isStatsLoading ? (
                  <Skeleton className="h-10 w-32 bg-gray-200/50" />
                ) : (
                  <div className="flex items-center">
                    <CurrencyDollar size={28} className="text-[#8a745c] mr-2" />
                    <span className="text-[#181510] text-3xl font-bold">${totalCreditsIssued.toLocaleString()}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-[#f1edea] to-[#e5ccb2] border-none shadow-md overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510]">Total Credits Redeemed</CardTitle>
              </CardHeader>
              <CardContent>
                {isStatsLoading ? (
                  <Skeleton className="h-10 w-32 bg-gray-200/50" />
                ) : (
                  <div className="flex items-center">
                    <QrCode size={28} className="text-[#8a745c] mr-2" />
                    <span className="text-[#181510] text-3xl font-bold">${totalCreditsRedeemed.toLocaleString()}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Tabs for different sections */}
          <Tabs defaultValue="shops" className="w-full">
            <TabsList className="w-full justify-start mb-6 bg-transparent p-0 h-auto">
              <TabsTrigger
                value="shops"
                className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510] rounded-lg mr-2"
              >
                <Storefront className="mr-2 h-5 w-5" />
                My Shops
              </TabsTrigger>
              <TabsTrigger
                value="quick-actions"
                className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510] rounded-lg"
              >
                <CurrencyDollar className="mr-2 h-5 w-5" />
                Quick Actions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="shops" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center">
                    <Storefront className="mr-2 h-5 w-5" />
                    My Shops
                  </CardTitle>
                  <CardDescription>Manage your merchant shops</CardDescription>
                </CardHeader>
                <CardContent>
                  {isShopsLoading ? (
                    <div className="space-y-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex items-center gap-4 p-4 border rounded-lg">
                          <Skeleton className="h-14 w-14 rounded-lg bg-gray-200" />
                          <div className="flex-1">
                            <Skeleton className="h-5 w-32 bg-gray-200 mb-2" />
                            <Skeleton className="h-4 w-48 bg-gray-200" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : shops && shops.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {shops.map((shop: MerchantShop) => (
                        <Link href={`/dashboard/merchant/shop/${shop.slug}`} key={shop.id} className="block">
                          <Card className="hover:shadow-md transition-shadow overflow-hidden h-full">
                            <div className="flex items-center gap-4 p-4">
                              <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-14 bg-[#e6e0db] flex items-center justify-center flex-shrink-0">
                                <Storefront size={32} weight="regular" className="text-[#8a745c]" />
                              </div>
                              <div className="flex flex-col justify-center flex-1 min-w-0">
                                <p className="text-[#181510] text-base font-medium leading-normal line-clamp-1">{shop.name}</p>
                                <p className="text-[#8a745c] text-sm font-normal leading-normal line-clamp-2">{shop.description || "No description"}</p>
                              </div>
                              <div className="shrink-0">
                                <div className="text-[#181510] flex size-7 items-center justify-center">
                                  <CaretRight size={24} weight="regular" />
                                </div>
                              </div>
                            </div>
                          </Card>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="p-8 text-center">
                      <Storefront size={48} weight="regular" className="text-[#8a745c] mx-auto mb-4" />
                      <p className="text-[#8a745c] mb-4">You don&apos;t have any shops yet.</p>
                      <Button
                        onClick={() => router.push("/dashboard/merchant/new")}
                        className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                      >
                        Create Your First Shop
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="quick-actions" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center">
                    <CurrencyDollar className="mr-2 h-5 w-5" />
                    Quick Actions
                  </CardTitle>
                  <CardDescription>Common tasks and shortcuts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="overflow-hidden hover:shadow-md transition-shadow">
                      <Link href="/dashboard/merchant/credits" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-[#e5ccb2] rounded-full p-3">
                            <CurrencyDollar size={24} className="text-[#181510]" />
                          </div>
                          <div>
                            <h3 className="font-medium text-[#181510]">Manage Credits</h3>
                            <p className="text-sm text-[#8a745c]">View and manage customer credits</p>
                          </div>
                        </div>
                      </Link>
                    </Card>

                    <Card className="overflow-hidden hover:shadow-md transition-shadow">
                      <Link href="/dashboard/merchant/codes" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-[#e5ccb2] rounded-full p-3">
                            <QrCode size={24} className="text-[#181510]" />
                          </div>
                          <div>
                            <h3 className="font-medium text-[#181510]">Credit Codes</h3>
                            <p className="text-sm text-[#8a745c]">Generate and manage credit codes</p>
                          </div>
                        </div>
                      </Link>
                    </Card>

                    <Card className="overflow-hidden hover:shadow-md transition-shadow">
                      <Link href="/dashboard/merchant/new" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-[#e5ccb2] rounded-full p-3">
                            <Plus size={24} className="text-[#181510]" />
                          </div>
                          <div>
                            <h3 className="font-medium text-[#181510]">Create Shop</h3>
                            <p className="text-sm text-[#8a745c]">Add a new merchant shop</p>
                          </div>
                        </div>
                      </Link>
                    </Card>

                    <Card className="overflow-hidden hover:shadow-md transition-shadow">
                      <Link href="/dashboard/merchant/profile" className="block p-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-[#e5ccb2] rounded-full p-3">
                            <User size={24} className="text-[#181510]" />
                          </div>
                          <div>
                            <h3 className="font-medium text-[#181510]">Profile</h3>
                            <p className="text-sm text-[#8a745c]">View and edit your profile</p>
                          </div>
                        </div>
                      </Link>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Mobile Navigation - Hidden on Desktop */}
      <div className="md:hidden">
        <div className="flex gap-2 border-t border-[#f1edea] bg-[#fbfaf9] px-4 pb-3 pt-2">
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#181510]" href="/dashboard/merchant">
            <div className="text-[#181510] flex h-8 items-center justify-center">
              <House size={24} weight="fill" />
            </div>
            <p className="text-[#181510] text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/credits">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <CurrencyDollar size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Credits</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/codes">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <QrCode size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Codes</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/profile">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <User size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Profile</p>
          </Link>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
