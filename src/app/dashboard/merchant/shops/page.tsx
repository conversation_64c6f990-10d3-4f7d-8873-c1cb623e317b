"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { Plus, Storefront, CurrencyDollar, User, House, CaretRight, Trash, Pencil } from "phosphor-react";
import Link from "next/link";
import {
  useGetMerchantShopsQuery,
  useCreateMerchantShopMutation,
  useUpdateMerchantShopMutation,
  useDeleteMerchantShopMutation
} from "@/lib/api/apiSlice";
import { MerchantShop } from "@/types";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function MerchantShopsPage() {
  const { data: session } = useSession();
  const { data: shops, isLoading, refetch } = useGetMerchantShopsQuery();
  const [createShop, { isLoading: isCreating }] = useCreateMerchantShopMutation();
  const [updateShop, { isLoading: isUpdating }] = useUpdateMerchantShopMutation();
  const [deleteShop, { isLoading: isDeleting }] = useDeleteMerchantShopMutation();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newShop, setNewShop] = useState({ name: "", description: "" });
  const [editShop, setEditShop] = useState<{ id: string; name: string; description: string }>({ id: "", name: "", description: "" });
  const [shopToDelete, setShopToDelete] = useState<string | null>(null);

  const { toast } = useToast();
  const router = useRouter();

  const handleCreateShop = async () => {
    try {
      await createShop(newShop).unwrap();
      toast({
        title: "Shop created",
        description: "Your shop has been created successfully."
      });
      setIsCreateDialogOpen(false);
      setNewShop({ name: "", description: "" });
      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create shop. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleUpdateShop = async () => {
    try {
      await updateShop({
        id: editShop.id,
        name: editShop.name,
        description: editShop.description
      }).unwrap();
      toast({
        title: "Shop updated",
        description: "Your shop has been updated successfully."
      });
      setIsEditDialogOpen(false);
      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update shop. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteShop = async () => {
    if (!shopToDelete) return;

    try {
      await deleteShop(shopToDelete).unwrap();
      toast({
        title: "Shop deleted",
        description: "Your shop has been deleted successfully."
      });
      setShopToDelete(null);
      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete shop. Please try again.",
        variant: "destructive"
      });
    }
  };

  const openEditDialog = (shop: MerchantShop) => {
    setEditShop({
      id: shop.id,
      name: shop.name,
      description: shop.description
    });
    setIsEditDialogOpen(true);
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div>
        <div className="flex items-center bg-[#fbfaf9] p-4 pb-2 justify-between">
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pl-12">Shops</h2>
          <div className="flex w-12 items-center justify-end">
            <Button
              className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 bg-transparent text-[#181510] gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0"
              variant="ghost"
              onClick={() => setIsCreateDialogOpen(true)}
            >
              <Plus size={24} weight="regular" className="text-[#181510]" />
            </Button>
          </div>
        </div>

        <div className="p-4">
          <Card>
            <CardHeader>
              <CardTitle>My Shops</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center p-4">Loading shops...</div>
              ) : shops && shops.length > 0 ? (
                <div className="space-y-4">
                  {shops.map((shop: MerchantShop) => (
                    <div key={shop.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-14 bg-[#e6e0db] flex items-center justify-center">
                          <Storefront size={32} weight="regular" className="text-[#8a745c]" />
                        </div>
                        <div>
                          <h3 className="font-medium">{shop.name}</h3>
                          <p className="text-sm text-muted-foreground line-clamp-1">{shop.description}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="icon" onClick={() => openEditDialog(shop)}>
                          <Pencil size={16} />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="icon" className="text-red-500">
                              <Trash size={16} />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the shop
                                and all associated data including customers and credit codes.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => {
                                  setShopToDelete(shop.id);
                                  handleDeleteShop();
                                }}
                                className="bg-red-500 hover:bg-red-600"
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                        <Link href={`/dashboard/merchant/${shop.id}`}>
                          <Button>View Details</Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4">
                  <p className="mb-4">You don't have any shops yet.</p>
                  <Button onClick={() => setIsCreateDialogOpen(true)}>Create Your First Shop</Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Create Shop Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Shop</DialogTitle>
            <DialogDescription>
              Add a new shop to your merchant account.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Shop Name</Label>
              <Input
                id="name"
                value={newShop.name}
                onChange={(e) => setNewShop({ ...newShop, name: e.target.value })}
                placeholder="Enter shop name"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newShop.description}
                onChange={(e) => setNewShop({ ...newShop, description: e.target.value })}
                placeholder="Enter shop description"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateShop} disabled={isCreating || !newShop.name}>
              {isCreating ? "Creating..." : "Create Shop"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Shop Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Shop</DialogTitle>
            <DialogDescription>
              Update your shop details.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Shop Name</Label>
              <Input
                id="edit-name"
                value={editShop.name}
                onChange={(e) => setEditShop({ ...editShop, name: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={editShop.description}
                onChange={(e) => setEditShop({ ...editShop, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateShop} disabled={isUpdating || !editShop.name}>
              {isUpdating ? "Updating..." : "Update Shop"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div>
        <div className="flex gap-2 border-t border-[#f1edea] bg-[#fbfaf9] px-4 pb-3 pt-2">
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <House size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/credits">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <CurrencyDollar size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Credits</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#181510]" href="/dashboard/merchant/shops">
            <div className="text-[#181510] flex h-8 items-center justify-center">
              <Storefront size={24} weight="fill" />
            </div>
            <p className="text-[#181510] text-xs font-medium leading-normal tracking-[0.015em]">Shops</p>
          </Link>
          <Link className="just flex flex-1 flex-col items-center justify-end gap-1 text-[#8a745c]" href="/dashboard/merchant/profile">
            <div className="text-[#8a745c] flex h-8 items-center justify-center">
              <User size={24} weight="regular" />
            </div>
            <p className="text-[#8a745c] text-xs font-medium leading-normal tracking-[0.015em]">Profile</p>
          </Link>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>
    </div>
  );
}
