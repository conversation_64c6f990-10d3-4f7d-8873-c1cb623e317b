"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MerchantBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { MerchantPageHeader } from "@/components/layout/page-header-mobile";
import { 
  ChartBar, 
  ChartLine, 
  ChartPie, 
  CreditCard, 
  Storefront, 
  User, 
  QrCode, 
  ArrowUp, 
  ArrowDown, 
  Calendar, 
  CaretRight 
} from "phosphor-react";
import { 
  useGetMerchantShopsQuery, 
  useGetMerchantCreditStatsQuery,
  useGetShopCustomersQuery,
  useGetCreditCodesQuery
} from "@/lib/api/apiSlice";
import { MerchantShop, ShopCustomer, CreditCode } from "@/types";

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });
};

// Helper function to calculate days ago
const daysAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export default function MerchantAnalyticsPage() {
  const router = useRouter();
  const [selectedPeriod, setSelectedPeriod] = useState("all");
  const [selectedShop, setSelectedShop] = useState<string | null>(null);

  // Fetch merchant shops
  const { 
    data: shops, 
    isLoading: isShopsLoading 
  } = useGetMerchantShopsQuery();

  // Fetch merchant credit stats
  const { 
    data: creditStats, 
    isLoading: isStatsLoading 
  } = useGetMerchantCreditStatsQuery();

  // Fetch shop customers if a shop is selected
  const { 
    data: customers, 
    isLoading: isCustomersLoading 
  } = useGetShopCustomersQuery(selectedShop || "", { 
    skip: !selectedShop 
  });

  // Fetch credit codes if a shop is selected
  const { 
    data: creditCodes, 
    isLoading: isCodesLoading 
  } = useGetCreditCodesQuery(selectedShop || "", { 
    skip: !selectedShop 
  });

  // Set the first shop as selected when shops are loaded
  useEffect(() => {
    if (shops && shops.length > 0 && !selectedShop) {
      setSelectedShop(shops[0].id);
    }
  }, [shops, selectedShop]);

  // Calculate analytics data
  const calculateAnalytics = () => {
    if (!shops || !creditStats) return null;

    // Basic stats
    const totalShops = shops.length;
    const totalCreditsIssued = creditStats.total_credits_issued;
    const totalCreditsRedeemed = creditStats.total_credits_redeemed;
    const creditsBalance = totalCreditsIssued - totalCreditsRedeemed;

    // Customer stats
    let totalCustomers = 0;
    let totalActiveCustomers = 0;
    let totalInactiveCustomers = 0;

    if (customers) {
      totalCustomers = customers.length;
      totalActiveCustomers = customers.filter(c => c.credit_balance > 0).length;
      totalInactiveCustomers = totalCustomers - totalActiveCustomers;
    }

    // Credit code stats
    let totalCodes = 0;
    let redeemedCodes = 0;
    let pendingCodes = 0;
    let expiredCodes = 0;

    if (creditCodes) {
      totalCodes = creditCodes.length;
      redeemedCodes = creditCodes.filter(c => c.is_redeemed).length;
      pendingCodes = creditCodes.filter(c => !c.is_redeemed && (!c.expires_at || new Date(c.expires_at) > new Date())).length;
      expiredCodes = creditCodes.filter(c => !c.is_redeemed && c.expires_at && new Date(c.expires_at) <= new Date()).length;
    }

    return {
      totalShops,
      totalCreditsIssued,
      totalCreditsRedeemed,
      creditsBalance,
      totalCustomers,
      totalActiveCustomers,
      totalInactiveCustomers,
      totalCodes,
      redeemedCodes,
      pendingCodes,
      expiredCodes
    };
  };

  const analytics = calculateAnalytics();

  // Loading state
  if (isShopsLoading || isStatsLoading) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
        <MerchantPageHeader
          title="Analytics"
          backHref="/dashboard/merchant"
        />
        <div className="p-4 flex flex-col gap-4">
          <Skeleton className="h-8 w-48 bg-gray-200" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Skeleton className="h-32 w-full bg-gray-200 rounded-lg" />
            <Skeleton className="h-32 w-full bg-gray-200 rounded-lg" />
            <Skeleton className="h-32 w-full bg-gray-200 rounded-lg" />
            <Skeleton className="h-32 w-full bg-gray-200 rounded-lg" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
      {/* Header */}
      <MerchantPageHeader
        title="Analytics"
        backHref="/dashboard/merchant"
      />

      {/* Breadcrumbs */}
      <MerchantBreadcrumbs
        segments={[
          { label: "Dashboard", href: "/dashboard/merchant" },
          { label: "Analytics", href: "/dashboard/merchant/analytics", isCurrentPage: true }
        ]}
      />

      <div className="md:max-w-6xl md:mx-auto md:px-6 md:py-4">
        {/* Shop Selector */}
        {shops && shops.length > 0 && (
          <div className="p-4 md:p-0 mb-4">
            <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
              <h2 className="text-[#181510] text-xl font-bold">Merchant Analytics</h2>
              <div className="flex gap-2">
                <Select
                  value={selectedShop || undefined}
                  onValueChange={(value) => setSelectedShop(value)}
                >
                  <SelectTrigger className="w-[200px] bg-white">
                    <SelectValue placeholder="Select shop" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Shops</SelectItem>
                    {shops.map((shop) => (
                      <SelectItem key={shop.id} value={shop.id}>
                        {shop.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={selectedPeriod}
                  onValueChange={setSelectedPeriod}
                >
                  <SelectTrigger className="w-[150px] bg-white">
                    <SelectValue placeholder="Time period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Time</SelectItem>
                    <SelectItem value="month">This Month</SelectItem>
                    <SelectItem value="week">This Week</SelectItem>
                    <SelectItem value="day">Today</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Content */}
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-6">
            <TabsTrigger value="overview" className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510]">
              <ChartBar className="mr-2 h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="customers" className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510]">
              <User className="mr-2 h-4 w-4" />
              Customers
            </TabsTrigger>
            <TabsTrigger value="codes" className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#181510]">
              <QrCode className="mr-2 h-4 w-4" />
              Credit Codes
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center text-lg">
                    <CreditCard className="mr-2 h-5 w-5 text-[#8a745c]" />
                    Credit Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-[#8a745c]">Total Credits Issued</span>
                    <span className="text-[#181510] font-bold">${analytics?.totalCreditsIssued || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#8a745c]">Total Credits Redeemed</span>
                    <span className="text-[#181510] font-bold">${analytics?.totalCreditsRedeemed || 0}</span>
                  </div>
                  <div className="flex justify-between items-center pt-2 border-t">
                    <span className="text-[#181510] font-medium">Current Balance</span>
                    <span className="text-[#181510] font-bold">${analytics?.creditsBalance || 0}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center text-lg">
                    <Storefront className="mr-2 h-5 w-5 text-[#8a745c]" />
                    Shop Statistics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-[#8a745c]">Total Shops</span>
                    <span className="text-[#181510] font-bold">{analytics?.totalShops || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#8a745c]">Total Customers</span>
                    <span className="text-[#181510] font-bold">{analytics?.totalCustomers || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#8a745c]">Total Credit Codes</span>
                    <span className="text-[#181510] font-bold">{analytics?.totalCodes || 0}</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Shop List */}
            {shops && shops.length > 0 && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center text-lg">
                    <Storefront className="mr-2 h-5 w-5 text-[#8a745c]" />
                    Your Shops
                  </CardTitle>
                  <CardDescription>
                    Overview of all your merchant shops
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {shops.map((shop) => (
                      <div 
                        key={shop.id} 
                        className="flex items-center justify-between p-4 bg-[#f1edea] rounded-lg hover:bg-[#e5e0dc] cursor-pointer"
                        onClick={() => router.push(`/dashboard/merchant/shop/${shop.slug}`)}
                      >
                        <div className="flex items-center gap-3">
                          <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-12 bg-[#e6e0db] flex items-center justify-center">
                            <Storefront size={24} weight="regular" className="text-[#8a745c]" />
                          </div>
                          <div>
                            <h3 className="text-[#181510] font-medium">{shop.name}</h3>
                            <p className="text-[#8a745c] text-sm">{shop.description || "No description"}</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span className="text-[#181510] mr-2">View Details</span>
                          <CaretRight size={16} className="text-[#181510]" />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Customers Tab */}
          <TabsContent value="customers" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] flex items-center text-lg">
                  <User className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Customer Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-[#f1edea] p-4 rounded-lg">
                    <div className="text-[#8a745c] mb-2">Total Customers</div>
                    <div className="text-[#181510] text-2xl font-bold">{analytics?.totalCustomers || 0}</div>
                  </div>
                  <div className="bg-[#f1edea] p-4 rounded-lg">
                    <div className="text-[#8a745c] mb-2">Active Customers</div>
                    <div className="text-[#181510] text-2xl font-bold">{analytics?.totalActiveCustomers || 0}</div>
                  </div>
                  <div className="bg-[#f1edea] p-4 rounded-lg">
                    <div className="text-[#8a745c] mb-2">Inactive Customers</div>
                    <div className="text-[#181510] text-2xl font-bold">{analytics?.totalInactiveCustomers || 0}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customer List */}
            {customers && customers.length > 0 ? (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center text-lg">
                    <User className="mr-2 h-5 w-5 text-[#8a745c]" />
                    Customer List
                  </CardTitle>
                  <CardDescription>
                    {selectedShop === "all" ? "All customers across your shops" : `Customers for ${shops?.find(s => s.id === selectedShop)?.name || "selected shop"}`}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {customers.map((customer) => (
                      <div key={customer.id} className="flex items-center justify-between p-4 bg-[#f1edea] rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-12 bg-[#e6e0db] flex items-center justify-center">
                            <User size={24} weight="regular" className="text-[#8a745c]" />
                          </div>
                          <div>
                            <h3 className="text-[#181510] font-medium">{customer.user?.name || "Customer"}</h3>
                            <p className="text-[#8a745c] text-sm">{customer.user?.email || ""}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-[#181510] font-bold">${customer.credit_balance}</div>
                          <div className="text-[#8a745c] text-sm">Credit Balance</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => router.push(`/dashboard/merchant/shop/${shops?.find(s => s.id === selectedShop)?.slug}/customers`)}
                  >
                    View All Customers
                  </Button>
                </CardFooter>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <User size={48} className="mx-auto text-[#8a745c] mb-4" />
                  <p className="text-[#8a745c] mb-4">No customers found for the selected shop.</p>
                  <Button
                    onClick={() => router.push(`/dashboard/merchant/shop/${shops?.find(s => s.id === selectedShop)?.slug}`)}
                    className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                  >
                    Go to Shop
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Credit Codes Tab */}
          <TabsContent value="codes" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] flex items-center text-lg">
                  <QrCode className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Credit Code Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-[#f1edea] p-4 rounded-lg">
                    <div className="text-[#8a745c] mb-2">Total Codes</div>
                    <div className="text-[#181510] text-2xl font-bold">{analytics?.totalCodes || 0}</div>
                  </div>
                  <div className="bg-[#f1edea] p-4 rounded-lg">
                    <div className="text-[#8a745c] mb-2">Redeemed</div>
                    <div className="text-[#181510] text-2xl font-bold">{analytics?.redeemedCodes || 0}</div>
                  </div>
                  <div className="bg-[#f1edea] p-4 rounded-lg">
                    <div className="text-[#8a745c] mb-2">Pending</div>
                    <div className="text-[#181510] text-2xl font-bold">{analytics?.pendingCodes || 0}</div>
                  </div>
                  <div className="bg-[#f1edea] p-4 rounded-lg">
                    <div className="text-[#8a745c] mb-2">Expired</div>
                    <div className="text-[#181510] text-2xl font-bold">{analytics?.expiredCodes || 0}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Credit Codes List */}
            {creditCodes && creditCodes.length > 0 ? (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-[#181510] flex items-center text-lg">
                    <QrCode className="mr-2 h-5 w-5 text-[#8a745c]" />
                    Recent Credit Codes
                  </CardTitle>
                  <CardDescription>
                    {selectedShop === "all" ? "Credit codes across all shops" : `Credit codes for ${shops?.find(s => s.id === selectedShop)?.name || "selected shop"}`}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {creditCodes.slice(0, 5).map((code) => (
                      <div key={code.id} className="flex items-center justify-between p-4 bg-[#f1edea] rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-12 flex items-center justify-center ${
                            code.is_redeemed ? "bg-[#d9b99a]" : code.expires_at && new Date(code.expires_at) <= new Date() ? "bg-[#e6e0db]" : "bg-[#e5ccb2]"
                          }`}>
                            <QrCode size={24} weight="regular" className="text-[#181510]" />
                          </div>
                          <div>
                            <h3 className="text-[#181510] font-medium font-mono">{code.code}</h3>
                            <p className="text-[#8a745c] text-sm">
                              {code.is_redeemed 
                                ? `Redeemed on ${formatDate(code.redeemed_at || "")}` 
                                : code.expires_at && new Date(code.expires_at) <= new Date() 
                                  ? `Expired on ${formatDate(code.expires_at)}` 
                                  : code.expires_at 
                                    ? `Expires on ${formatDate(code.expires_at)}` 
                                    : "No expiration"
                              }
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-[#181510] font-bold">${code.amount}</div>
                          <div className={`text-sm ${
                            code.is_redeemed ? "text-green-600" : code.expires_at && new Date(code.expires_at) <= new Date() ? "text-red-600" : "text-[#8a745c]"
                          }`}>
                            {code.is_redeemed ? "Redeemed" : code.expires_at && new Date(code.expires_at) <= new Date() ? "Expired" : "Active"}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => router.push(`/dashboard/merchant/shop/${shops?.find(s => s.id === selectedShop)?.slug}/codes`)}
                  >
                    View All Credit Codes
                  </Button>
                </CardFooter>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <QrCode size={48} className="mx-auto text-[#8a745c] mb-4" />
                  <p className="text-[#8a745c] mb-4">No credit codes found for the selected shop.</p>
                  <Button
                    onClick={() => router.push(`/dashboard/merchant/shop/${shops?.find(s => s.id === selectedShop)?.slug}/generate-code`)}
                    className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                  >
                    Generate New Code
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
