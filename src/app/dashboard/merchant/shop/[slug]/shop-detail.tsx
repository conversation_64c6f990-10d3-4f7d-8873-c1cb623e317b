"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, CreditCard, QrCode, User, Phone, Calendar } from "phosphor-react";
import { MerchantBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { MerchantPageHeader } from "@/components/layout/page-header-mobile";
import { PaymentQRCode } from "@/components/merchant/payment-qr-code";
import {
  useGetMerchantShopBySlugQuery,
  useGetShopCustomersQuery,
  useAddShopCustomerMutation,
  useAddShopCreditMutation,
  useGenerateCreditCodeMutation
} from "@/lib/api/apiSlice";
import { ShopCustomer } from "@/types";
import { Mail } from "lucide-react";

interface ShopDetailProps {
  shopSlug: string;
}

export default function ShopDetail({ shopSlug }: ShopDetailProps) {
  const router = useRouter();
  const { toast } = useToast();

  // Fetch shop and customer data
  const { data: shop, isLoading: isShopLoading, refetch: refetchShop } = useGetMerchantShopBySlugQuery(shopSlug);
  const { data: customers, isLoading: isCustomersLoading, refetch: refetchCustomers } =
    useGetShopCustomersQuery(shop?.id || "", { skip: !shop?.id });

  // Mutations
  const [addCustomer] = useAddShopCustomerMutation();
  const [addCredit] = useAddShopCreditMutation();
  const [generateCode] = useGenerateCreditCodeMutation();

  // Dialog states
  const [isAddCustomerDialogOpen, setIsAddCustomerDialogOpen] = useState(false);
  const [isAddCreditDialogOpen, setIsAddCreditDialogOpen] = useState(false);
  const [isGenerateCodeDialogOpen, setIsGenerateCodeDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<ShopCustomer | null>(null);

  // Form states
  const [newCustomer, setNewCustomer] = useState({ name: "", email: "" });
  const [creditAmount, setCreditAmount] = useState(0);
  const [creditDescription, setCreditDescription] = useState("");
  const [codeAmount, setCodeAmount] = useState(0);
  const [codeDescription, setCodeDescription] = useState("");
  const [codeExpiresIn, setCodeExpiresIn] = useState(30);

  // Handle adding a new customer
  const handleAddCustomer = async () => {
    if (!shop?.id || !newCustomer.name || !newCustomer.email) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      await addCustomer({
        shopId: shop.id,
        name: newCustomer.name,
        email: newCustomer.email
      }).unwrap();

      toast({
        title: "Success",
        description: "Customer added successfully",
      });

      setNewCustomer({ name: "", email: "" });
      setIsAddCustomerDialogOpen(false);
      refetchCustomers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to add customer",
        variant: "destructive",
      });
    }
  };

  // Handle adding credit to a customer
  const handleAddCredit = async () => {
    if (!shop?.id || !selectedCustomer) {
      toast({
        title: "Error",
        description: "Please select a customer",
        variant: "destructive",
      });
      return;
    }

    if (creditAmount <= 0) {
      toast({
        title: "Error",
        description: "Credit amount must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    try {
      await addCredit({
        shopId: shop.id,
        customerId: selectedCustomer.id,
        amount: creditAmount,
        description: creditDescription
      }).unwrap();

      toast({
        title: "Success",
        description: `${creditAmount} credits added to ${selectedCustomer.user?.name}`,
      });

      setSelectedCustomer(null);
      setCreditAmount(0);
      setCreditDescription("");
      setIsAddCreditDialogOpen(false);
      refetchCustomers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to add credits",
        variant: "destructive",
      });
    }
  };

  // Handle generating a credit code
  const handleGenerateCode = async () => {
    if (!shop?.id || codeAmount <= 0) {
      toast({
        title: "Error",
        description: "Code amount must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await generateCode({
        shopId: shop.id,
        amount: codeAmount,
        description: codeDescription,
        expiresIn: codeExpiresIn
      }).unwrap();

      toast({
        title: "Success",
        description: "Credit code generated successfully",
      });

      setCodeAmount(0);
      setCodeDescription("");
      setIsGenerateCodeDialogOpen(false);

      // Navigate to the code detail page to show the QR code
      router.push(`/dashboard/merchant/shop/${shopSlug}/codes/${result.credit_code.id}`);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to generate code",
        variant: "destructive",
      });
    }
  };

  // Loading state with skeleton UI
  if (isShopLoading) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
        <div className="p-4 text-center">
          <div className="flex items-center justify-center">
            <div className="h-32 w-32 rounded-full bg-gray-200 animate-pulse"></div>
          </div>
          <div className="mt-4 h-6 w-48 mx-auto bg-gray-200 animate-pulse rounded"></div>
          <div className="mt-2 h-4 w-64 mx-auto bg-gray-200 animate-pulse rounded"></div>
          <div className="mt-8 h-6 w-32 bg-gray-200 animate-pulse rounded"></div>
          <div className="mt-4 space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center gap-4">
                <div className="h-14 w-14 rounded-full bg-gray-200 animate-pulse"></div>
                <div className="flex-1">
                  <div className="h-5 w-32 bg-gray-200 animate-pulse rounded"></div>
                  <div className="mt-1 h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (!shop) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
        <div className="p-4 text-center">
          <h2 className="text-[#181510] text-lg font-bold">Shop not found</h2>
          <p className="text-[#8a745c] mt-2">The shop you're looking for doesn't exist or you don't have access to it.</p>
          <Button
            className="mt-4 bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            onClick={() => router.push('/dashboard/merchant')}
          >
            Return to Shops
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div className="flex-1">
        {/* Header */}
        <MerchantPageHeader
          title="Shop Details"
          backHref="/dashboard/merchant"
        />

        {/* Breadcrumbs */}
        <MerchantBreadcrumbs
          segments={[
            { label: "Shops", href: "/dashboard/merchant" },
            { label: shop?.name || "Shop Details", href: `/dashboard/merchant/shop/${shopSlug}`, isCurrentPage: true }
          ]}
        />

        <div className="md:max-w-6xl md:mx-auto md:px-6 md:py-4">
          {/* Shop Profile Card */}
          <Card className="mb-6 overflow-hidden">
            <div className="bg-gradient-to-r from-[#e5ccb2] to-[#f1edea] h-32 relative"></div>
            <div className="px-6 pb-6 pt-0 -mt-16 flex flex-col items-center">
              <div className="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-32 w-32 border-4 border-white shadow-md"
                style={{
                  backgroundImage: shop?.image_url
                    ? `url("${shop.image_url}")`
                    : 'url("https://via.placeholder.com/128?text=Shop")'
                }}
              />
              <h2 className="text-[#181510] text-2xl font-bold mt-4">{shop.name}</h2>
              <p className="text-[#8a745c] text-center max-w-md mt-2">{shop.description || "No description"}</p>

              <div className="flex flex-wrap gap-4 mt-4 justify-center">
                {shop.contact_email && (
                  <div className="flex items-center gap-2 text-[#8a745c]">
                    <Mail size={18} />
                    <span>{shop.contact_email}</span>
                  </div>
                )}
                {shop.contact_phone && (
                  <div className="flex items-center gap-2 text-[#8a745c]">
                    <Phone size={18} />
                    <span>{shop.contact_phone}</span>
                  </div>
                )}
                <div className="flex items-center gap-2 text-[#8a745c]">
                  <Calendar size={18} />
                  <span>Created {new Date(shop.created_at).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </Card>

          {/* Tabs for Customers and Actions */}
          <Tabs defaultValue="customers" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="customers">Customers</TabsTrigger>
              <TabsTrigger value="actions">Actions</TabsTrigger>
            </TabsList>

            <TabsContent value="customers" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Customers</h3>
                <Button
                  onClick={() => setIsAddCustomerDialogOpen(true)}
                  className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                >
                  <Plus size={18} className="mr-2" /> Add Customer
                </Button>
              </div>

              {/* Customers List */}
              {isCustomersLoading ? (
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <Card key={i} className="p-4">
                      <div className="flex items-center gap-4">
                        <div className="h-14 w-14 rounded-full bg-gray-200 animate-pulse"></div>
                        <div className="flex-1">
                          <div className="h-5 w-32 bg-gray-200 animate-pulse rounded"></div>
                          <div className="mt-1 h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : customers && customers.length > 0 ? (
                <div className="space-y-3">
                  {customers.map((customer) => (
                    <Card key={customer.id} className="overflow-hidden hover:shadow-md transition-shadow">
                      <div
                        className="flex items-center gap-4 p-4 cursor-pointer"
                        onClick={() => {
                          setSelectedCustomer(customer);
                          setIsAddCreditDialogOpen(true);
                        }}
                      >
                        <div
                          className="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-14 w-14 flex-shrink-0"
                          style={{
                            backgroundImage: 'url("https://via.placeholder.com/56?text=User")'
                          }}
                        />
                        <div className="flex flex-col justify-center flex-1 min-w-0">
                          <p className="text-[#181510] text-base font-medium leading-normal line-clamp-1">
                            {customer.user?.name || "Customer"}
                          </p>
                          <div className="flex flex-wrap gap-x-4 text-[#8a745c] text-sm">
                            <p className="line-clamp-1 font-semibold">
                              Credit: ${customer.credit_balance || 0}
                            </p>
                            {customer.user?.email && (
                              <p className="line-clamp-1 hidden sm:block">
                                {customer.user.email}
                              </p>
                            )}
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="ml-auto"
                        >
                          <CreditCard size={16} className="mr-2" /> Add Credit
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card className="p-8 text-center">
                  <User size={48} className="mx-auto text-[#8a745c] mb-4" />
                  <p className="text-[#8a745c] mb-4">No customers yet.</p>
                  <Button
                    onClick={() => setIsAddCustomerDialogOpen(true)}
                    className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                  >
                    Add Your First Customer
                  </Button>
                </Card>
              )}

              {customers && customers.length > 0 && (
                <div className="text-center mt-4">
                  <Link href={`/dashboard/merchant/shop/${shopSlug}/customers`}>
                    <Button variant="link">View all {customers.length} customers</Button>
                  </Link>
                </div>
              )}
            </TabsContent>

            <TabsContent value="actions" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Manage Credits</CardTitle>
                    <CardDescription>Add or manage customer credits</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-[#8a745c] mb-4">Add credits to customer accounts or view transaction history.</p>
                  </CardContent>
                  <CardFooter>
                    <Button
                      className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                      onClick={() => setIsAddCreditDialogOpen(true)}
                    >
                      <CreditCard className="mr-2 h-5 w-5" />
                      <span>Manage Credits</span>
                    </Button>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Generate Code</CardTitle>
                    <CardDescription>Create credit codes for customers</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-[#8a745c] mb-4">Generate QR codes that customers can scan to redeem credits.</p>
                  </CardContent>
                  <CardFooter>
                    <Link href={`/dashboard/merchant/shop/${shopSlug}/generate-code`} className="w-full">
                      <Button
                        className="w-full bg-[#f1edea] text-[#181510] hover:bg-[#e5e0dc]"
                      >
                        <QrCode className="mr-2 h-5 w-5" />
                        <span>Generate Code</span>
                      </Button>
                    </Link>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Payment QR Code</CardTitle>
                    <CardDescription>Generate a QR code for customer payments</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-[#8a745c] mb-4">Create a QR code that customers can scan to pay with their credits.</p>
                  </CardContent>
                  <CardFooter>
                    {shop && shop.id && (
                      <PaymentQRCode shopId={shop.id} shopName={shop.name} />
                    )}
                  </CardFooter>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Add Customer Dialog */}
      <Dialog open={isAddCustomerDialogOpen} onOpenChange={setIsAddCustomerDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add New Customer</DialogTitle>
            <DialogDescription>
              Add a new customer to your shop
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={newCustomer.name}
                onChange={(e) => setNewCustomer({ ...newCustomer, name: e.target.value })}
                placeholder="Customer name"
                className="w-full"
                autoComplete="off"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={newCustomer.email}
                onChange={(e) => setNewCustomer({ ...newCustomer, email: e.target.value })}
                placeholder="<EMAIL>"
                className="w-full"
                autoComplete="off"
              />
            </div>
          </div>
          <DialogFooter className="sm:justify-between">
            <Button
              variant="outline"
              onClick={() => {
                setNewCustomer({ name: "", email: "" });
                setIsAddCustomerDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddCustomer}
              disabled={!newCustomer.name || !newCustomer.email}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            >
              Add Customer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Credit Dialog */}
      <Dialog open={isAddCreditDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setSelectedCustomer(null);
          setCreditAmount(0);
          setCreditDescription("");
        }
        setIsAddCreditDialogOpen(open);
      }}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Credits</DialogTitle>
            <DialogDescription>
              Add credits to a customer's account
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {!selectedCustomer && customers && customers.length > 0 ? (
              <div className="grid gap-2">
                <Label htmlFor="customer">Select Customer</Label>
                <select
                  id="customer"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={selectedCustomer?.id || ""}
                  onChange={(e) => {
                    const customer = customers.find(c => c.id === e.target.value);
                    if (customer) setSelectedCustomer(customer);
                  }}
                >
                  <option value="" disabled>Select a customer</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>
                      {customer.user?.name || "Customer"} (${customer.credit_balance || 0})
                    </option>
                  ))}
                </select>
              </div>
            ) : !selectedCustomer && (!customers || customers.length === 0) ? (
              <div className="text-center p-4 border rounded-md bg-muted">
                <p>No customers available.</p>
                <Button
                  variant="link"
                  onClick={() => {
                    setIsAddCreditDialogOpen(false);
                    setIsAddCustomerDialogOpen(true);
                  }}
                  className="mt-2"
                >
                  Add a customer first
                </Button>
              </div>
            ) : null}

            {selectedCustomer && (
              <div className="p-4 border rounded-md bg-muted">
                <p className="font-medium">Selected Customer:</p>
                <p>{selectedCustomer.user?.name || "Customer"}</p>
                <p className="mt-2">Current Balance: ${selectedCustomer.credit_balance || 0}</p>
                {selectedCustomer.user?.email && (
                  <p className="text-sm text-muted-foreground mt-1">{selectedCustomer.user.email}</p>
                )}
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="amount">Credit Amount</Label>
              <Input
                id="amount"
                type="number"
                min="1"
                value={creditAmount || ""}
                onChange={(e) => setCreditAmount(parseInt(e.target.value) || 0)}
                placeholder="Amount to add"
                className="w-full"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Input
                id="description"
                value={creditDescription}
                onChange={(e) => setCreditDescription(e.target.value)}
                placeholder="Reason for adding credits"
                className="w-full"
              />
            </div>
          </div>
          <DialogFooter className="sm:justify-between">
            <Button
              variant="outline"
              onClick={() => {
                setSelectedCustomer(null);
                setCreditAmount(0);
                setCreditDescription("");
                setIsAddCreditDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddCredit}
              disabled={!selectedCustomer || creditAmount <= 0}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            >
              Add Credits
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
