"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Plus, CreditCard, QrCode } from "phosphor-react";
import { MerchantBreadcrumbs } from "@/components/navigation/breadcrumbs";
import { MerchantPageHeader } from "@/components/layout/page-header-mobile";
import {
  useGetMerchantShopQuery,
  useGetShopCustomersQuery,
  useAddShopCustomerMutation,
  useAddShopCreditMutation,
  useGenerateCreditCodeMutation
} from "@/lib/api/apiSlice";
import { MerchantShop, ShopCustomer } from "@/types";

interface ShopDetailProps {
  shopId: string;
}

export default function ShopDetail({ shopId }: ShopDetailProps) {
  const router = useRouter();
  const { toast } = useToast();

  // Fetch shop and customer data
  const { data: shop, isLoading: isShopLoading, refetch: refetchShop } = useGetMerchantShopQuery(shopId);
  const { data: customers, isLoading: isCustomersLoading, refetch: refetchCustomers } = useGetShopCustomersQuery(shopId);

  // Mutations
  const [addCustomer] = useAddShopCustomerMutation();
  const [addCredit] = useAddShopCreditMutation();
  const [generateCode] = useGenerateCreditCodeMutation();

  // Dialog states
  const [isAddCustomerDialogOpen, setIsAddCustomerDialogOpen] = useState(false);
  const [isAddCreditDialogOpen, setIsAddCreditDialogOpen] = useState(false);
  const [isGenerateCodeDialogOpen, setIsGenerateCodeDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<ShopCustomer | null>(null);

  // Form states
  const [newCustomer, setNewCustomer] = useState({ name: "", email: "" });
  const [creditAmount, setCreditAmount] = useState(0);
  const [creditDescription, setCreditDescription] = useState("");
  const [codeAmount, setCodeAmount] = useState(0);
  const [codeDescription, setCodeDescription] = useState("");
  const [codeExpiresIn, setCodeExpiresIn] = useState(30);

  // Handle adding a new customer
  const handleAddCustomer = async () => {
    if (!newCustomer.name || !newCustomer.email) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      await addCustomer({
        shopId,
        name: newCustomer.name,
        email: newCustomer.email
      }).unwrap();

      toast({
        title: "Success",
        description: "Customer added successfully",
      });

      setNewCustomer({ name: "", email: "" });
      setIsAddCustomerDialogOpen(false);
      refetchCustomers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to add customer",
        variant: "destructive",
      });
    }
  };

  // Handle adding credit to a customer
  const handleAddCredit = async () => {
    if (!selectedCustomer) {
      toast({
        title: "Error",
        description: "Please select a customer",
        variant: "destructive",
      });
      return;
    }

    if (creditAmount <= 0) {
      toast({
        title: "Error",
        description: "Credit amount must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    try {
      await addCredit({
        shopId,
        customerId: selectedCustomer.id,
        amount: creditAmount,
        description: creditDescription
      }).unwrap();

      toast({
        title: "Success",
        description: `${creditAmount} credits added to ${selectedCustomer.user?.name}`,
      });

      setSelectedCustomer(null);
      setCreditAmount(0);
      setCreditDescription("");
      setIsAddCreditDialogOpen(false);
      refetchCustomers();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to add credits",
        variant: "destructive",
      });
    }
  };

  // Handle generating a credit code
  const handleGenerateCode = async () => {
    if (codeAmount <= 0) {
      toast({
        title: "Error",
        description: "Code amount must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await generateCode({
        shopId,
        amount: codeAmount,
        description: codeDescription,
        expiresIn: codeExpiresIn
      }).unwrap();

      toast({
        title: "Success",
        description: "Credit code generated successfully",
      });

      setCodeAmount(0);
      setCodeDescription("");
      setIsGenerateCodeDialogOpen(false);

      // Navigate to the code detail page to show the QR code
      router.push(`/dashboard/merchant/${shopId}/codes/${result.credit_code.id}`);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.message || "Failed to generate code",
        variant: "destructive",
      });
    }
  };

  // Loading state with skeleton UI
  if (isShopLoading) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
        <div className="p-4 text-center">
          <div className="flex items-center justify-center">
            <div className="h-32 w-32 rounded-full bg-gray-200 animate-pulse"></div>
          </div>
          <div className="mt-4 h-6 w-48 mx-auto bg-gray-200 animate-pulse rounded"></div>
          <div className="mt-2 h-4 w-64 mx-auto bg-gray-200 animate-pulse rounded"></div>
          <div className="mt-8 h-6 w-32 bg-gray-200 animate-pulse rounded"></div>
          <div className="mt-4 space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center gap-4">
                <div className="h-14 w-14 rounded-full bg-gray-200 animate-pulse"></div>
                <div className="flex-1">
                  <div className="h-5 w-32 bg-gray-200 animate-pulse rounded"></div>
                  <div className="mt-1 h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (!shop) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between overflow-x-hidden">
        <div className="p-4 text-center">
          <h2 className="text-[#181510] text-lg font-bold">Shop not found</h2>
          <p className="text-[#8a745c] mt-2">The shop you're looking for doesn't exist or you don't have access to it.</p>
          <Button
            className="mt-4 bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            onClick={() => router.push('/dashboard/merchant')}
          >
            Return to Shops
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] justify-between group/design-root overflow-x-hidden">
      <div>
        {/* Header */}
        <MerchantPageHeader
          title="Shop Details"
          backHref="/dashboard/merchant"
        />

        {/* Breadcrumbs */}
        <MerchantBreadcrumbs
          segments={[
            { label: "Shops", href: "/dashboard/merchant" },
            { label: shop?.name || "Shop Details", href: `/dashboard/merchant/${shopId}`, isCurrentPage: true }
          ]}
        />

        {/* Shop Profile */}
        <div className="flex p-4 w-full">
          <div className="flex w-full flex-col gap-4 items-center">
            <div className="flex gap-4 flex-col items-center">
              <div
                className="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-24 w-24 md:min-h-32 md:w-32"
                style={{
                  backgroundImage: shop?.image_url
                    ? `url("${shop.image_url}")`
                    : 'url("https://via.placeholder.com/128?text=Shop")'
                }}
              />
              <div className="flex flex-col items-center justify-center">
                <p className="text-[#181510] text-xl md:text-[22px] font-bold leading-tight tracking-[-0.015em] text-center">{shop?.name}</p>
                <p className="text-[#8a745c] text-sm md:text-base font-normal leading-normal text-center max-w-md">{shop?.description || "No description"}</p>
                {shop?.contact_email && (
                  <p className="text-[#8a745c] text-sm font-normal mt-1">
                    Email: {shop.contact_email}
                  </p>
                )}
                {shop?.contact_phone && (
                  <p className="text-[#8a745c] text-sm font-normal">
                    Phone: {shop.contact_phone}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Customers Section */}
        <div className="flex justify-between items-center">
          <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Customers</h3>
          <Link href={`/dashboard/merchant/${shopId}/customers`}>
            <Button
              variant="ghost"
              size="sm"
              className="mr-4"
            >
              View All
            </Button>
          </Link>
        </div>

        {/* Customers List */}
        {isCustomersLoading ? (
          <div className="p-4 space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center gap-4">
                <div className="h-14 w-14 rounded-full bg-gray-200 animate-pulse"></div>
                <div className="flex-1">
                  <div className="h-5 w-32 bg-gray-200 animate-pulse rounded"></div>
                  <div className="mt-1 h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
                </div>
              </div>
            ))}
          </div>
        ) : customers && customers.length > 0 ? (
          <div className="space-y-1">
            {customers.slice(0, 3).map((customer) => (
              <div
                key={customer.id}
                className="flex items-center gap-4 bg-[#fbfaf9] px-4 min-h-[72px] py-2 hover:bg-[#f1edea] cursor-pointer transition-colors"
                onClick={() => {
                  setSelectedCustomer(customer);
                  setIsAddCreditDialogOpen(true);
                }}
              >
                <div
                  className="bg-center bg-no-repeat aspect-square bg-cover rounded-full h-14 w-14 flex-shrink-0"
                  style={{
                    backgroundImage: 'url("https://via.placeholder.com/56?text=User")'
                  }}
                />
                <div className="flex flex-col justify-center flex-1 min-w-0">
                  <p className="text-[#181510] text-base font-medium leading-normal line-clamp-1">
                    {customer.user?.name || "Customer"}
                  </p>
                  <div className="flex flex-wrap gap-x-4 text-[#8a745c] text-sm">
                    <p className="line-clamp-1">
                      Credit: ${customer.credit_balance || 0}
                    </p>
                    {customer.user?.email && (
                      <p className="line-clamp-1 hidden sm:block">
                        {customer.user.email}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-4 text-center text-[#8a745c]">
            <p>No customers yet.</p>
            <Button
              variant="link"
              onClick={() => setIsAddCustomerDialogOpen(true)}
              className="mt-2"
            >
              Add your first customer to get started
            </Button>
          </div>
        )}

        {customers && customers.length > 3 && (
          <div className="px-4 py-2 text-center">
            <Link href={`/dashboard/merchant/${shopId}/customers`}>
              <Button variant="link">View all {customers.length} customers</Button>
            </Link>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div>
        <div className="flex justify-stretch">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full px-4 py-3">
            <Button
              className="flex cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-5 bg-[#e5ccb2] text-[#181510] text-base font-bold leading-normal tracking-[0.015em] hover:bg-[#d9b99a]"
              onClick={() => setIsAddCreditDialogOpen(true)}
            >
              <CreditCard className="mr-2 h-5 w-5" />
              <span className="truncate">Manage Credits</span>
            </Button>
            <Link href={`/dashboard/merchant/${shopId}/generate-code`}>
              <Button
                className="flex w-full cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 px-5 bg-[#f1edea] text-[#181510] text-base font-bold leading-normal tracking-[0.015em] hover:bg-[#e5e0dc]"
              >
                <QrCode className="mr-2 h-5 w-5" />
                <span className="truncate">Generate Code</span>
              </Button>
            </Link>
          </div>
        </div>
        <div className="h-5 bg-[#fbfaf9]"></div>
      </div>

      {/* Add Customer Dialog */}
      <Dialog open={isAddCustomerDialogOpen} onOpenChange={setIsAddCustomerDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add New Customer</DialogTitle>
            <DialogDescription>
              Add a new customer to your shop
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={newCustomer.name}
                onChange={(e) => setNewCustomer({ ...newCustomer, name: e.target.value })}
                placeholder="Customer name"
                className="w-full"
                autoComplete="off"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={newCustomer.email}
                onChange={(e) => setNewCustomer({ ...newCustomer, email: e.target.value })}
                placeholder="<EMAIL>"
                className="w-full"
                autoComplete="off"
              />
            </div>
          </div>
          <DialogFooter className="sm:justify-between">
            <Button
              variant="outline"
              onClick={() => {
                setNewCustomer({ name: "", email: "" });
                setIsAddCustomerDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddCustomer}
              disabled={!newCustomer.name || !newCustomer.email}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            >
              Add Customer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Credit Dialog */}
      <Dialog open={isAddCreditDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setSelectedCustomer(null);
          setCreditAmount(0);
          setCreditDescription("");
        }
        setIsAddCreditDialogOpen(open);
      }}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Credits</DialogTitle>
            <DialogDescription>
              Add credits to a customer's account
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {!selectedCustomer && customers && customers.length > 0 ? (
              <div className="grid gap-2">
                <Label htmlFor="customer">Select Customer</Label>
                <select
                  id="customer"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={selectedCustomer?.id || ""}
                  onChange={(e) => {
                    const customer = customers.find(c => c.id === e.target.value);
                    if (customer) setSelectedCustomer(customer);
                  }}
                >
                  <option value="" disabled>Select a customer</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>
                      {customer.user?.name || "Customer"} (${customer.credit_balance || 0})
                    </option>
                  ))}
                </select>
              </div>
            ) : !selectedCustomer && (!customers || customers.length === 0) ? (
              <div className="text-center p-4 border rounded-md bg-muted">
                <p>No customers available.</p>
                <Button
                  variant="link"
                  onClick={() => {
                    setIsAddCreditDialogOpen(false);
                    setIsAddCustomerDialogOpen(true);
                  }}
                  className="mt-2"
                >
                  Add a customer first
                </Button>
              </div>
            ) : null}

            {selectedCustomer && (
              <div className="p-4 border rounded-md bg-muted">
                <p className="font-medium">Selected Customer:</p>
                <p>{selectedCustomer.user?.name || "Customer"}</p>
                <p className="mt-2">Current Balance: ${selectedCustomer.credit_balance || 0}</p>
                {selectedCustomer.user?.email && (
                  <p className="text-sm text-muted-foreground mt-1">{selectedCustomer.user.email}</p>
                )}
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="amount">Credit Amount</Label>
              <Input
                id="amount"
                type="number"
                min="1"
                value={creditAmount || ""}
                onChange={(e) => setCreditAmount(parseInt(e.target.value) || 0)}
                placeholder="Amount to add"
                className="w-full"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Input
                id="description"
                value={creditDescription}
                onChange={(e) => setCreditDescription(e.target.value)}
                placeholder="Reason for adding credits"
                className="w-full"
              />
            </div>
          </div>
          <DialogFooter className="sm:justify-between">
            <Button
              variant="outline"
              onClick={() => {
                setSelectedCustomer(null);
                setCreditAmount(0);
                setCreditDescription("");
                setIsAddCreditDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddCredit}
              disabled={!selectedCustomer || creditAmount <= 0}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            >
              Add Credits
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Generate Code Dialog */}
      <Dialog open={isGenerateCodeDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setCodeAmount(0);
          setCodeDescription("");
        }
        setIsGenerateCodeDialogOpen(open);
      }}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Generate Credit Code</DialogTitle>
            <DialogDescription>
              Create a credit code that customers can redeem
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="codeAmount">Credit Amount</Label>
              <Input
                id="codeAmount"
                type="number"
                min="1"
                value={codeAmount || ""}
                onChange={(e) => setCodeAmount(parseInt(e.target.value) || 0)}
                placeholder="Amount of credits"
                className="w-full"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="codeDescription">Description (Optional)</Label>
              <Input
                id="codeDescription"
                value={codeDescription}
                onChange={(e) => setCodeDescription(e.target.value)}
                placeholder="Purpose of this code"
                className="w-full"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="expiresIn">Expires In (Days)</Label>
              <Input
                id="expiresIn"
                type="number"
                min="0"
                value={codeExpiresIn || ""}
                onChange={(e) => setCodeExpiresIn(parseInt(e.target.value) || 0)}
                placeholder="0 for no expiration"
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">Set to 0 for no expiration</p>
            </div>
          </div>
          <DialogFooter className="sm:justify-between">
            <Button
              variant="outline"
              onClick={() => {
                setCodeAmount(0);
                setCodeDescription("");
                setIsGenerateCodeDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleGenerateCode}
              disabled={codeAmount <= 0}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            >
              Generate Code
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
