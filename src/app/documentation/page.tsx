"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import Link from "next/link";
import { Copy, Check, ExternalLink, ChevronRight } from "lucide-react";

// Code block component
const CodeBlock = ({ language, code }: { language: string; code: string }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative my-4">
      <div className="absolute right-2 top-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={copyToClipboard}
          className="h-8 w-8 rounded-md"
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
          <span className="sr-only">Copy code</span>
        </Button>
      </div>
      <div className="flex items-center gap-2 bg-muted px-4 py-2 rounded-t-md border border-border">
        <div className="text-xs font-medium">{language}</div>
      </div>
      <pre className="overflow-x-auto rounded-b-md bg-muted p-4 text-sm border border-t-0 border-border">
        <code className="font-mono">{code}</code>
      </pre>
    </div>
  );
};

export default function DocumentationPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />
      <main className="flex-1 container py-10">
        <h1 className="text-3xl font-bold mb-4">ADC Credit Documentation</h1>
        <div className="flex items-center gap-4 mb-8">
          <Link href="/documentation/api-reference" className="text-primary hover:underline flex items-center">
            <span>API Reference</span>
            <ChevronRight className="h-4 w-4 ml-1" />
          </Link>
        </div>

        <Tabs defaultValue="getting-started" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="getting-started">Getting Started</TabsTrigger>
            <TabsTrigger value="api-keys">API Keys</TabsTrigger>
            <TabsTrigger value="credits">Credits</TabsTrigger>
            <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
            <TabsTrigger value="sdk">SDK</TabsTrigger>
          </TabsList>

          {/* Getting Started Section */}
          <TabsContent value="getting-started" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Getting Started with ADC Credit</CardTitle>
                <CardDescription>
                  Learn how to integrate the ADC Credit system into your applications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <h3 className="text-lg font-semibold">Introduction</h3>
                <p>
                  ADC Credit is a credit-based API management system that allows you to:
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Track and manage API usage with credits</li>
                  <li>Generate and manage API keys with specific permissions</li>
                  <li>Set up webhooks for real-time notifications</li>
                  <li>View detailed analytics on API usage</li>
                  <li>Manage organizations and branches for team collaboration</li>
                </ul>

                <h3 className="text-lg font-semibold mt-6">Quick Start</h3>
                <ol className="list-decimal pl-6 space-y-4">
                  <li>
                    <strong>Sign up for an account</strong>
                    <p className="mt-1">
                      Create an account at{" "}
                      <Link href="/auth/signup" className="text-primary underline">
                        our signup page
                      </Link>
                      .
                    </p>
                  </li>
                  <li>
                    <strong>Create an API key</strong>
                    <p className="mt-1">
                      Navigate to the{" "}
                      <Link href="/dashboard/api-keys" className="text-primary underline">
                        API Keys section
                      </Link>{" "}
                      in your dashboard and create a new API key.
                    </p>
                  </li>
                  <li>
                    <strong>Integrate with your application</strong>
                    <p className="mt-1">
                      Use our SDK or direct API calls to integrate with your application.
                    </p>
                  </li>
                </ol>

                <h3 className="text-lg font-semibold mt-6">Base URL</h3>
                <p className="font-mono bg-muted p-2 rounded">
                  https://api.adccredit.com/api/v1
                </p>

                <h3 className="text-lg font-semibold mt-6">Authentication</h3>
                <p>
                  All API requests require authentication using an API key. Include your API key in the request header:
                </p>

                <CodeBlock
                  language="HTTP"
                  code={`GET /api/v1/credits
Authorization: Bearer YOUR_API_KEY`}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* API Keys Section */}
          <TabsContent value="api-keys" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>API Keys</CardTitle>
                <CardDescription>
                  Managing and using API keys for authentication
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <h3 className="text-lg font-semibold">Creating API Keys</h3>
                <p>
                  API keys are used to authenticate your requests to the ADC Credit API. You can create multiple API keys with different permissions for different applications or services.
                </p>

                <h4 className="text-md font-semibold mt-4">API Key Permissions</h4>
                <p>When creating an API key, you can assign specific permissions:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li><strong>Read</strong> - Access to read-only endpoints</li>
                  <li><strong>Write</strong> - Access to create and update resources</li>
                  <li><strong>Delete</strong> - Access to delete resources</li>
                  <li><strong>Admin</strong> - Full access to all endpoints</li>
                </ul>

                <h3 className="text-lg font-semibold mt-6">Using API Keys</h3>
                <p>
                  Include your API key in the Authorization header of your requests:
                </p>

                <CodeBlock
                  language="JavaScript"
                  code={`// Example using fetch
fetch('https://api.adccredit.com/api/v1/credits', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data));`}
                />

                <h3 className="text-lg font-semibold mt-6">API Key Security</h3>
                <p>
                  Keep your API keys secure and never expose them in client-side code. If an API key is compromised, you should immediately disable it and create a new one.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Credits Section */}
          <TabsContent value="credits" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Credits System</CardTitle>
                <CardDescription>
                  Understanding and managing credits for API usage
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <h3 className="text-lg font-semibold">How Credits Work</h3>
                <p>
                  The ADC Credit system uses credits to track and manage API usage. Each API request consumes a specific number of credits based on the endpoint and operation.
                </p>

                <h4 className="text-md font-semibold mt-4">Credit Allocation</h4>
                <p>
                  Credits are allocated based on your subscription tier:
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li><strong>Free Tier</strong> - 1,000 credits per month</li>
                  <li><strong>Pro Tier</strong> - 10,000 credits per month</li>
                  <li><strong>Enterprise Tier</strong> - 50,000 credits per month</li>
                </ul>

                <h3 className="text-lg font-semibold mt-6">Checking Credit Balance</h3>
                <p>
                  You can check your current credit balance using the credits endpoint:
                </p>

                <CodeBlock
                  language="HTTP"
                  code={`GET /api/v1/credits
Authorization: Bearer YOUR_API_KEY

Response:
{
  "credit_balance": 8500,
  "credit_limit": 10000,
  "subscription": {
    "id": "sub_123",
    "subscription_tier_id": 2,
    "subscription_tier": {
      "name": "Pro",
      "credit_limit": 10000,
      ...
    },
    ...
  }
}`}
                />

                <h3 className="text-lg font-semibold mt-6">Credit Usage</h3>
                <p>
                  When your application makes API requests, credits are consumed automatically. You can view your credit usage history in the dashboard or via the API.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Webhooks Section */}
          <TabsContent value="webhooks" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Webhooks</CardTitle>
                <CardDescription>
                  Setting up and managing webhooks for real-time notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <h3 className="text-lg font-semibold">What are Webhooks?</h3>
                <p>
                  Webhooks allow you to receive real-time notifications when specific events occur in your ADC Credit account. When an event happens, we'll send an HTTP POST request to the URL you specify.
                </p>

                <h4 className="text-md font-semibold mt-4">Available Events</h4>
                <ul className="list-disc pl-6 space-y-2">
                  <li><strong>credit.consumed</strong> - When credits are consumed by an API request</li>
                  <li><strong>credit.added</strong> - When credits are added to your account</li>
                  <li><strong>credit.reset</strong> - When credits are reset (monthly)</li>
                  <li><strong>api_key.created</strong> - When a new API key is created</li>
                  <li><strong>api_key.disabled</strong> - When an API key is disabled</li>
                </ul>

                <h3 className="text-lg font-semibold mt-6">Creating a Webhook</h3>
                <p>
                  You can create webhooks in the dashboard or via the API:
                </p>

                <CodeBlock
                  language="JavaScript"
                  code={`// Example webhook creation
fetch('https://api.adccredit.com/api/v1/webhooks', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'Credit Consumption Webhook',
    url: 'https://your-app.com/webhooks/adc-credit',
    secret: 'your_webhook_secret',
    events: ['credit.consumed', 'credit.added']
  })
})
.then(response => response.json())
.then(data => console.log(data));`}
                />

                <h3 className="text-lg font-semibold mt-6">Webhook Security</h3>
                <p>
                  Each webhook includes a signature header that you can use to verify the request came from ADC Credit. The signature is an HMAC SHA-256 hash of the request body using your webhook secret.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* SDK Section */}
          <TabsContent value="sdk" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>SDK Integration</CardTitle>
                <CardDescription>
                  Using the ADC Credit SDK for easy integration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <h3 className="text-lg font-semibold">JavaScript/TypeScript SDK</h3>
                <p>
                  Our JavaScript/TypeScript SDK provides a simple way to integrate with the ADC Credit API.
                </p>

                <h4 className="text-md font-semibold mt-4">Installation</h4>
                <CodeBlock
                  language="bash"
                  code={`npm install adc-credit-sdk
# or
yarn add adc-credit-sdk`}
                />

                <h4 className="text-md font-semibold mt-4">Basic Usage</h4>
                <CodeBlock
                  language="TypeScript"
                  code={`import ADCCreditSDK from 'adc-credit-sdk';

// Initialize with API key
const sdk = new ADCCreditSDK({
  apiUrl: 'https://api.adccredit.com',
  apiKey: 'your-api-key',
  debug: true // Optional, enables verbose logging
});

// Check credit balance
const creditResponse = await sdk.credits.getBalance();
console.log('Credit balance:', creditResponse.data.credit_balance);

// Consume credits
const consumeResponse = await sdk.credits.consume({
  endpoint: '/api/data',
  method: 'GET',
  credits: 1
});
console.log('Credits consumed:', consumeResponse.data.credits);`}
                />

                <h3 className="text-lg font-semibold mt-6">SDK Modules</h3>
                <p>
                  The SDK provides the following modules:
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li><strong>auth</strong> - Authentication and user management</li>
                  <li><strong>apiKeys</strong> - API key management</li>
                  <li><strong>credits</strong> - Credit balance and transactions</li>
                  <li><strong>usage</strong> - Usage statistics and analytics</li>
                  <li><strong>webhooks</strong> - Webhook management</li>
                  <li><strong>users</strong> - User management</li>
                  <li><strong>organizations</strong> - Organization and branch management</li>
                  <li><strong>merchant</strong> - Merchant shop and customer management</li>
                  <li><strong>customer</strong> - Customer shop interactions and credit management</li>
                  <li><strong>subscriptions</strong> - Subscription plan management</li>
                </ul>

                <div className="mt-6">
                  <Link href="https://github.com/adc-credit/sdk" target="_blank" className="flex items-center text-primary hover:underline">
                    <span>View SDK on GitHub</span>
                    <ExternalLink className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
      <Footer />
    </div>
  );
}
