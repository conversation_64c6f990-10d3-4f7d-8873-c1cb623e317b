/**
 * ADC Credit SDK Usage Examples
 */
import ADCCreditSDK from '../sdk';

/**
 * Example: Basic SDK initialization
 */
async function basicExample() {
  // Initialize with API key
  const sdk = new ADCCreditSDK({
    apiUrl: 'http://localhost:8080',
    apiKey: 'your-api-key',
    debug: true
  });

  // Get credit balance
  const balanceResponse = await sdk.credits.getBalance();
  if (balanceResponse.error) {
    console.error(`Error: ${balanceResponse.error}`);
    return;
  }

  console.log(`Credit balance: ${balanceResponse.data.credit_balance}`);
  console.log(`Credit limit: ${balanceResponse.data.credit_limit}`);
}

/**
 * Example: Authentication flow
 */
async function authExample() {
  // Initialize SDK without authentication
  const sdk = new ADCCreditSDK({
    apiUrl: 'http://localhost:8080'
  });

  // Login
  const loginResponse = await sdk.auth.login('<EMAIL>', 'password');
  if (loginResponse.error) {
    console.error(`Login error: ${loginResponse.error}`);
    return;
  }

  // Update SDK with the new token
  sdk.updateConfig({ token: loginResponse.data.token });
  console.log(`Logged in as: ${loginResponse.data.user.name}`);

  // Get current user
  const userResponse = await sdk.auth.getCurrentUser();
  if (!userResponse.error) {
    console.log(`User ID: ${userResponse.data.id}`);
    console.log(`User email: ${userResponse.data.email}`);
  }
}

/**
 * Example: API key management
 */
async function apiKeyExample() {
  // Initialize SDK with JWT token
  const sdk = new ADCCreditSDK({
    apiUrl: 'http://localhost:8080',
    token: 'your-jwt-token'
  });

  // Create a new API key
  const createResponse = await sdk.apiKeys.create({
    name: 'My API Key',
    permissions: ['read', 'write']
  });

  if (createResponse.error) {
    console.error(`Error creating API key: ${createResponse.error}`);
    return;
  }

  const apiKey = createResponse.data;
  console.log(`API key created: ${apiKey.name}`);
  console.log(`API key: ${apiKey.key}`);

  // Get all API keys
  const apiKeysResponse = await sdk.apiKeys.getAll();
  if (!apiKeysResponse.error) {
    console.log(`Found ${apiKeysResponse.data.length} API keys`);
    apiKeysResponse.data.forEach(key => {
      console.log(`- ${key.name} (${key.enabled ? 'enabled' : 'disabled'})`);
    });
  }
}

/**
 * Example: Credit consumption
 */
async function creditConsumptionExample() {
  // Initialize SDK with API key
  const sdk = new ADCCreditSDK({
    apiUrl: 'http://localhost:8080',
    apiKey: 'your-api-key'
  });

  // Consume credits
  const consumeResponse = await sdk.credits.consume({
    endpoint: '/api/data',
    method: 'GET',
    credits: 1,
    ip_address: '***********',
    user_agent: 'MyApp/1.0'
  });

  if (consumeResponse.error) {
    console.error(`Error consuming credits: ${consumeResponse.error}`);
    return;
  }

  console.log(`Credits consumed successfully`);
  console.log(`New credit balance: ${consumeResponse.data.credit_balance}`);
  console.log(`Usage ID: ${consumeResponse.data.usage.id}`);
}

/**
 * Example: Webhook management
 */
async function webhookExample() {
  // Initialize SDK with JWT token
  const sdk = new ADCCreditSDK({
    apiUrl: 'http://localhost:8080',
    token: 'your-jwt-token'
  });

  // Create a new webhook
  const createResponse = await sdk.webhooks.create({
    name: 'Credit Consumption Webhook',
    url: 'https://example.com/webhook',
    secret: 'webhook-secret',
    events: ['credit.consumed', 'credit.added']
  });

  if (createResponse.error) {
    console.error(`Error creating webhook: ${createResponse.error}`);
    return;
  }

  const webhook = createResponse.data;
  console.log(`Webhook created: ${webhook.name}`);
  console.log(`Webhook ID: ${webhook.id}`);

  // Get webhook deliveries
  const deliveriesResponse = await sdk.webhooks.getDeliveries(webhook.id);
  if (!deliveriesResponse.error) {
    console.log(`Found ${deliveriesResponse.data.length} webhook deliveries`);
    deliveriesResponse.data.forEach(delivery => {
      console.log(`- Event: ${delivery.event}, Success: ${delivery.success}`);
    });
  }
}

// Run examples
(async () => {
  try {
    console.log('Running basic example...');
    await basicExample();
    
    console.log('\nRunning authentication example...');
    await authExample();
    
    console.log('\nRunning API key example...');
    await apiKeyExample();
    
    console.log('\nRunning credit consumption example...');
    await creditConsumptionExample();
    
    console.log('\nRunning webhook example...');
    await webhookExample();
  } catch (error) {
    console.error('Error running examples:', error);
  }
})();
