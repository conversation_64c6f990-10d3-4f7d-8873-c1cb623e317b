/**
 * ADC Credit SDK User Management Example
 */
import ADCCreditSDK from '../sdk';

/**
 * Example: User management
 */
async function userManagementExample() {
  // Initialize SDK with JWT token
  const sdk = new ADCCreditSDK({
    apiUrl: 'http://localhost:8080',
    token: 'your-jwt-token',
    debug: true
  });

  try {
    // Get current user
    console.log('\n1. Getting current user...');
    const currentUserResponse = await sdk.users.getCurrentUser();
    
    if (currentUserResponse.error) {
      console.error(`Error getting current user: ${currentUserResponse.error}`);
      return;
    }
    
    const currentUser = currentUserResponse.data;
    console.log(`Current user: ${currentUser.name} (${currentUser.email})`);
    console.log(`Role: ${currentUser.role}`);
    
    // Update current user
    console.log('\n2. Updating current user...');
    const updateResponse = await sdk.users.updateCurrentUser({
      name: `${currentUser.name} (Updated)`,
    });
    
    if (updateResponse.error) {
      console.error(`Error updating user: ${updateResponse.error}`);
    } else {
      console.log(`User updated: ${updateResponse.data.name}`);
    }
    
    // Check if user is admin
    if (currentUser.role === 'admin') {
      // Admin: Get all users
      console.log('\n3. Getting all users (admin only)...');
      const usersResponse = await sdk.users.getAllUsers();
      
      if (usersResponse.error) {
        console.error(`Error getting users: ${usersResponse.error}`);
      } else {
        console.log(`Found ${usersResponse.data.length} users`);
        usersResponse.data.forEach(user => {
          console.log(`- ${user.name} (${user.email}), Role: ${user.role}`);
        });
      }
    }
    
    // Get organizations
    console.log('\n4. Getting organizations...');
    const orgsResponse = await sdk.organizations.getAll();
    
    if (orgsResponse.error) {
      console.error(`Error getting organizations: ${orgsResponse.error}`);
    } else {
      console.log(`Found ${orgsResponse.data.length} organizations`);
      
      if (orgsResponse.data.length > 0) {
        const organization = orgsResponse.data[0];
        console.log(`Organization: ${organization.name} (${organization.slug})`);
        
        // Get branches
        console.log('\n5. Getting branches...');
        const branchesResponse = await sdk.organizations.getBranches(organization.id);
        
        if (branchesResponse.error) {
          console.error(`Error getting branches: ${branchesResponse.error}`);
        } else {
          console.log(`Found ${branchesResponse.data.length} branches`);
          
          if (branchesResponse.data.length > 0) {
            const branch = branchesResponse.data[0];
            console.log(`Branch: ${branch.name}`);
            
            // Get external users
            console.log('\n6. Getting external users...');
            const externalUsersResponse = await sdk.users.getExternalUsers({
              organization_id: organization.id,
              branch_id: branch.id
            });
            
            if (externalUsersResponse.error) {
              console.error(`Error getting external users: ${externalUsersResponse.error}`);
            } else {
              console.log(`Found ${externalUsersResponse.data.length} external users`);
              externalUsersResponse.data.forEach(user => {
                console.log(`- ${user.name} (${user.email}), Credits: ${user.monthly_credits}`);
              });
              
              // Create a new external user
              console.log('\n7. Creating a new external user...');
              const createResponse = await sdk.users.createExternalUser({
                organization_id: organization.id,
                branch_id: branch.id,
                name: 'New External User',
                email: `external-${Date.now()}@example.com`,
                monthly_credits: 100
              });
              
              if (createResponse.error) {
                console.error(`Error creating external user: ${createResponse.error}`);
              } else {
                const newUser = createResponse.data;
                console.log(`Created external user: ${newUser.name} (${newUser.email})`);
                console.log(`Monthly credits: ${newUser.monthly_credits}`);
                
                // Add credits to the external user
                console.log('\n8. Adding credits to external user...');
                const addCreditsResponse = await sdk.users.addCreditsToExternalUser(
                  newUser.id,
                  50,
                  'Manual credit addition'
                );
                
                if (addCreditsResponse.error) {
                  console.error(`Error adding credits: ${addCreditsResponse.error}`);
                } else {
                  console.log(`Credits added successfully. New balance: ${addCreditsResponse.data.credit_balance}`);
                }
              }
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('Error running example:', error);
  }
}

// Run the example
(async () => {
  try {
    console.log('Running user management example...');
    await userManagementExample();
  } catch (error) {
    console.error('Error running example:', error);
  }
})();
