/**
 * ADC Credit SDK Merchant and Customer Usage Examples
 */
import ADCCreditSDK from '../sdk';

/**
 * Example: Merchant shop management
 */
async function merchantShopExample() {
  // Initialize with JWT token
  const sdk = new ADCCreditSDK({
    apiUrl: 'http://localhost:8080',
    token: 'your-jwt-token',
    debug: true
  });

  // Get all merchant shops
  const shopsResponse = await sdk.merchant.getShops();
  if (shopsResponse.error) {
    console.error(`Error fetching shops: ${shopsResponse.error}`);
    return;
  }

  console.log(`Found ${shopsResponse.data.length} shops`);

  // Create a new shop
  const createShopResponse = await sdk.merchant.createShop({
    name: 'My Coffee Shop',
    description: 'The best coffee in town',
    contact_email: '<EMAIL>',
    contact_phone: '+1234567890'
  });

  if (createShopResponse.error) {
    console.error(`Error creating shop: ${createShopResponse.error}`);
    return;
  }

  const shopId = createShopResponse.data.id;
  console.log(`Created shop with ID: ${shopId}`);

  // Add a customer to the shop
  const addCustomerResponse = await sdk.merchant.addShopCustomer(shopId, {
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890'
  });

  if (addCustomerResponse.error) {
    console.error(`Error adding customer: ${addCustomerResponse.error}`);
    return;
  }

  const customerId = addCustomerResponse.data.id;
  console.log(`Added customer with ID: ${customerId}`);

  // Add credit to the customer
  const addCreditResponse = await sdk.merchant.addShopCredit(shopId, {
    customer_id: customerId,
    amount: 50,
    description: 'Welcome bonus'
  });

  if (addCreditResponse.error) {
    console.error(`Error adding credit: ${addCreditResponse.error}`);
    return;
  }

  console.log(`Added ${addCreditResponse.data.credit_balance} credits to customer`);

  // Generate a credit code
  const generateCodeResponse = await sdk.merchant.generateCreditCode(shopId, {
    amount: 25,
    description: 'Promotional code',
    expires_at: '2023-12-31T23:59:59Z'
  });

  if (generateCodeResponse.error) {
    console.error(`Error generating code: ${generateCodeResponse.error}`);
    return;
  }

  console.log(`Generated credit code: ${generateCodeResponse.data.code}`);
  console.log(`Code amount: ${generateCodeResponse.data.amount}`);

  // Create a shop API key
  const createApiKeyResponse = await sdk.merchant.createShopApiKey(
    shopId,
    'POS Terminal',
    ['read', 'write']
  );

  if (createApiKeyResponse.error) {
    console.error(`Error creating API key: ${createApiKeyResponse.error}`);
    return;
  }

  console.log(`Created API key: ${createApiKeyResponse.data.key}`);
  console.log(`API key ID: ${createApiKeyResponse.data.id}`);
}

/**
 * Example: Customer shop interactions
 */
async function customerShopExample() {
  // Initialize with JWT token
  const sdk = new ADCCreditSDK({
    apiUrl: 'http://localhost:8080',
    token: 'your-jwt-token'
  });

  // Get all shops where user is a customer
  const shopsResponse = await sdk.customer.getShops();
  if (shopsResponse.error) {
    console.error(`Error fetching shops: ${shopsResponse.error}`);
    return;
  }

  console.log(`Found ${shopsResponse.data.length} shops`);

  if (shopsResponse.data.length === 0) {
    console.log('No shops found');
    return;
  }

  // Get the first shop
  const shopId = shopsResponse.data[0].id;
  const shopResponse = await sdk.customer.getShop(shopId);
  
  if (shopResponse.error) {
    console.error(`Error fetching shop: ${shopResponse.error}`);
    return;
  }

  console.log(`Shop name: ${shopResponse.data.name}`);
  console.log(`Credit balance: ${shopResponse.data.credit_balance}`);

  // Redeem a credit code
  const redeemResponse = await sdk.customer.redeemCreditCode({
    code: 'ABC123'
  });

  if (redeemResponse.error) {
    console.error(`Error redeeming code: ${redeemResponse.error}`);
  } else {
    console.log(`Redeemed code for ${redeemResponse.data.amount} credits`);
    console.log(`New credit balance: ${redeemResponse.data.credit_balance}`);
  }

  // Use shop credit
  const useResponse = await sdk.customer.useShopCredit(shopId, {
    amount: 10,
    description: 'Coffee purchase'
  });

  if (useResponse.error) {
    console.error(`Error using credit: ${useResponse.error}`);
    return;
  }

  console.log(`Used 10 credits`);
  console.log(`New credit balance: ${useResponse.data.credit_balance}`);

  // Get credit transaction history
  const transactionsResponse = await sdk.customer.getCreditTransactions(shopId);
  
  if (transactionsResponse.error) {
    console.error(`Error fetching transactions: ${transactionsResponse.error}`);
    return;
  }

  console.log(`Found ${transactionsResponse.data.length} transactions`);
  transactionsResponse.data.forEach(transaction => {
    console.log(`${transaction.type}: ${transaction.amount} credits - ${transaction.description}`);
  });
}

/**
 * Example: Subscription management
 */
async function subscriptionExample() {
  // Initialize with JWT token
  const sdk = new ADCCreditSDK({
    apiUrl: 'http://localhost:8080',
    token: 'your-jwt-token'
  });

  // Get all subscription tiers
  const tiersResponse = await sdk.subscriptions.getTiers();
  if (tiersResponse.error) {
    console.error(`Error fetching tiers: ${tiersResponse.error}`);
    return;
  }

  console.log(`Found ${tiersResponse.data.length} subscription tiers`);
  tiersResponse.data.forEach(tier => {
    console.log(`${tier.name}: $${tier.price} - ${tier.credit_limit} credits`);
  });

  // Get active subscription
  const activeResponse = await sdk.subscriptions.getActive();
  if (activeResponse.error) {
    console.error(`Error fetching active subscription: ${activeResponse.error}`);
  } else {
    console.log(`Active subscription: ${activeResponse.data.subscription_tier.name}`);
    console.log(`Credit balance: ${activeResponse.data.credit_balance}`);
  }

  // Create a new subscription
  const createResponse = await sdk.subscriptions.create({
    subscription_tier_id: 2, // Pro tier
    auto_renew: true
  });

  if (createResponse.error) {
    console.error(`Error creating subscription: ${createResponse.error}`);
    return;
  }

  console.log(`Created subscription to ${createResponse.data.subscription_tier.name}`);
  console.log(`Credit balance: ${createResponse.data.credit_balance}`);

  // Create a checkout session for subscription payment
  const checkoutResponse = await sdk.subscriptions.createCheckoutSession(2); // Pro tier
  
  if (checkoutResponse.error) {
    console.error(`Error creating checkout session: ${checkoutResponse.error}`);
    return;
  }

  console.log(`Checkout URL: ${checkoutResponse.data.checkout_url}`);
}

// Export examples
export {
  merchantShopExample,
  customerShopExample,
  subscriptionExample
};
