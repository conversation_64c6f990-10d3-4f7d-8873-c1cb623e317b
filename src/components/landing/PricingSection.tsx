'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { loadStripe } from '@stripe/stripe-js';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { SubscriptionTier } from '@/types';
import { getSubscriptionTiers } from '@/lib/api';

// Load Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || "");

// Stripe price IDs for each tier
const STRIPE_PRICE_IDS: Record<number, string> = {
  1: "price_1RQ6BwCtNXkGk5bXwZBAcIg2", // Free Tier - Replace with your actual price ID
  2: "price_1RQ6CCCtNXkGk5bXhyav7bZ5", // Pro Tier - Replace with your actual price ID
  3: "price_1RQ6CVCtNXkGk5bXytxxlvwx", // Enterprise Tier - Replace with your actual price ID
};

// Helper function to get access token from session
const getAccessToken = (session: ReturnType<typeof useSession>['data']): string | null => {
  if (!session) return null;

  if (session.accessToken) {
    return session.accessToken;
  } else if (session.user && 'accessToken' in session.user) {
    return session.user.accessToken as string;
  }

  return null;
};

export const PricingSection = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [subscriptionTiers, setSubscriptionTiers] = useState<SubscriptionTier[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTier, setSelectedTier] = useState<SubscriptionTier | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isCreatingCheckout, setIsCreatingCheckout] = useState(false);

  // Fetch subscription tiers
  useEffect(() => {
    async function fetchSubscriptionTiers() {
      try {
        // If user is authenticated, fetch from API
        if (status === 'authenticated' && session) {
          const accessToken = getAccessToken(session);
          if (accessToken) {
            const tiers = await getSubscriptionTiers(accessToken);
            setSubscriptionTiers(tiers);
            setLoading(false);
            return;
          }
        }

        // If not authenticated or no token, use fallback data
        setSubscriptionTiers([
          {
            id: 1,
            name: "Basic",
            description: "Perfect for individuals and small projects",
            price: 0,
            credit_limit: 100000,
            features: ["100K API Calls/month", "Basic Analytics", "Community Support"],
            default_rate_limit_max: 60,
            default_rate_limit_rate: 1,
            max_webhooks: 1,
            advanced_analytics: false,
            created_at: "",
            updated_at: ""
          },
          {
            id: 2,
            name: "Pro",
            description: "Ideal for growing businesses and teams",
            price: 49,
            credit_limit: 1000000,
            features: ["1M API Calls/month", "Advanced Analytics", "Priority Support", "Customizable Branding"],
            default_rate_limit_max: 120,
            default_rate_limit_rate: 1,
            max_webhooks: 5,
            advanced_analytics: true,
            created_at: "",
            updated_at: ""
          },
          {
            id: 3,
            name: "Enterprise",
            description: "For large-scale applications and organizations",
            price: 299,
            credit_limit: 10000000,
            features: ["Unlimited API Calls", "Dedicated Support", "Custom Integrations", "White Labeling"],
            default_rate_limit_max: 500,
            default_rate_limit_rate: 1,
            max_webhooks: 20,
            advanced_analytics: true,
            created_at: "",
            updated_at: ""
          }
        ]);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching subscription tiers:", error);
        setLoading(false);
      }
    }

    fetchSubscriptionTiers();
  }, [session, status]);

  const handleCreateSubscription = async () => {
    if (!session?.user || !selectedTier) {
      // If not logged in, redirect to login
      if (status !== 'authenticated') {
        router.push('/auth/signin');
        return;
      }
      return;
    }

    setIsCreatingCheckout(true);

    try {
      // Get the access token
      const accessToken = getAccessToken(session);

      if (!accessToken) {
        throw new Error('No access token available');
      }

      // Get the Stripe price ID for the selected tier
      const priceId = STRIPE_PRICE_IDS[selectedTier.id];

      if (!priceId) {
        throw new Error(`No Stripe price ID found for tier ${selectedTier.id}`);
      }

      // Create a checkout session
      const response = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          productName: selectedTier.name,
          tierId: selectedTier.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const { url } = await response.json();

      if (!url) {
        throw new Error('Failed to create checkout session');
      }

      // Close the dialog
      setConfirmDialogOpen(false);

      // Redirect to Stripe Checkout
      window.location.href = url;
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create checkout session");
    } finally {
      setIsCreatingCheckout(false);
    }
  };

  const handleSelectPlan = (tier: SubscriptionTier) => {
    setSelectedTier(tier);

    if (status !== 'authenticated') {
      // If not logged in, redirect to login
      router.push('/auth/signin');
      return;
    }

    setConfirmDialogOpen(true);
  };

  return (
    <section className="flex flex-col gap-8 md:gap-12 items-center" id="pricing">
      <div className="text-center max-w-3xl">
        <h2 className="text-foreground text-3xl md:text-4xl font-bold leading-tight tracking-tight mb-3">Flexible Pricing Plans</h2>
        <p className="text-muted-foreground text-base md:text-lg leading-relaxed">
          Choose the plan that fits your needs. From startups to enterprises, we have you covered.
        </p>
      </div>

      {loading ? (
        <div className="flex justify-center w-full py-12">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 w-full">
          {subscriptionTiers.map((tier, index) => {
            const isPopular = tier.id === 2; // Pro plan is popular
            const isPriceFree = tier.price === 0;

            return (
              <div
                key={tier.id}
                className={`flex flex-1 flex-col gap-6 rounded-xl ${isPopular ? 'border-2 border-primary scale-100 md:scale-105 relative overflow-hidden' : 'border border-accent'} bg-card p-6 md:p-8 transform transition-all duration-300 ${isPopular ? 'hover:shadow-2xl hover:shadow-primary/40' : 'hover:shadow-xl hover:border-primary/50'}`}
              >
                {isPopular && (
                  <div className="absolute top-0 right-0 bg-primary text-primary-foreground text-xs font-semibold px-3 py-1 rounded-bl-lg">POPULAR</div>
                )}
                <div className="flex flex-col gap-2">
                  <h3 className="text-card-foreground text-2xl font-semibold leading-tight">{tier.name}</h3>
                  <p className="flex items-baseline gap-1 text-card-foreground">
                    <span className="text-card-foreground text-5xl font-black leading-tight tracking-tighter">
                      {isPriceFree ? "Free" : `$${tier.price}`}
                    </span>
                    {!isPriceFree && <span className="text-muted-foreground text-base font-medium">/month</span>}
                  </p>
                  <p className="text-muted-foreground text-sm mt-1">{tier.description}</p>
                </div>

                <button
                  onClick={() => handleSelectPlan(tier)}
                  className={`w-full flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-11 px-5 ${isPopular ? 'bg-primary hover:bg-primary/90' : 'bg-accent hover:bg-primary'} ${isPopular ? 'text-primary-foreground' : 'text-accent-foreground hover:text-primary-foreground'} text-sm font-semibold leading-normal tracking-[0.015em] transition-colors`}
                >
                  <span className="truncate">
                    {isPriceFree ? "Get Started" : "Choose Plan"}
                  </span>
                </button>

                <div className="flex flex-col gap-3 pt-2">
                  <div className="text-sm font-normal leading-normal flex items-center gap-3 text-muted-foreground">
                    <div className="text-primary">
                      <svg className="font-bold" fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
                        <path d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"></path>
                      </svg>
                    </div>
                    {tier.credit_limit.toLocaleString()} API Credits/month
                  </div>

                  {Array.isArray(tier.features) && tier.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="text-sm font-normal leading-normal flex items-center gap-3 text-muted-foreground">
                      <div className="text-primary">
                        <svg className="font-bold" fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
                          <path d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"></path>
                        </svg>
                      </div>
                      {feature}
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Confirm Subscription Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to subscribe to the {selectedTier?.name} plan?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Plan:</span>
                <span className="font-medium">{selectedTier?.name}</span>
              </div>
              <div className="flex justify-between">
                <span>Price:</span>
                <span className="font-medium">
                  ${selectedTier?.price.toFixed(2)}/month
                </span>
              </div>
              <div className="flex justify-between">
                <span>Credits:</span>
                <span className="font-medium">
                  {selectedTier?.credit_limit.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
          <DialogFooter className="sm:justify-end">
            <Button variant="outline" size="sm" onClick={() => setConfirmDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleCreateSubscription}
              disabled={isCreatingCheckout}
            >
              {isCreatingCheckout ? (
                <>
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
                  Processing...
                </>
              ) : (
                "Confirm"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </section>
  );
};
