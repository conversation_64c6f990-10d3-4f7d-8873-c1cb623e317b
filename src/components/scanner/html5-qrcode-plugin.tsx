"use client";

import { useEffect, useRef } from "react";
import { Html5Qrcode } from "html5-qrcode";

interface Html5QrcodePluginProps {
  fps?: number;
  qrbox?: number;
  aspectRatio?: number;
  disableFlip?: boolean;
  qrCodeSuccessCallback: (decodedText: string, decodedResult: any) => void;
  qrCodeErrorCallback?: (error: any) => void;
}

const qrcodeRegionId = "html5qr-code-full-region";

const Html5QrcodePlugin = ({
  fps = 10,
  qrbox = 250,
  aspectRatio = 1.0,
  disableFlip = false,
  qrCodeSuccessCallback,
  qrCodeErrorCallback,
}: Html5QrcodePluginProps) => {
  const html5QrCodeRef = useRef<Html5Qrcode | null>(null);

  useEffect(() => {
    // Create instance of Html5Qrcode
    const html5QrCode = new Html5Qrcode(qrcodeRegionId);
    html5QrCodeRef.current = html5QrCode;

    // Start scanning
    const config = { fps, qrbox, aspectRatio, disableFlip };
    html5QrCode.start(
      { facingMode: "environment" }, // Use the back camera
      config,
      qrCodeSuccessCallback,
      qrCodeErrorCallback
    ).catch((err) => {
      console.error("Error starting QR code scanner:", err);
    });

    // Cleanup on unmount
    return () => {
      if (html5QrCodeRef.current && html5QrCodeRef.current.isScanning) {
        html5QrCodeRef.current
          .stop()
          .catch((err) => console.error("Error stopping QR code scanner:", err));
      }
    };
  }, [fps, qrbox, aspectRatio, disableFlip, qrCodeSuccessCallback, qrCodeErrorCallback]);

  return (
    <div className="w-full">
      <div 
        id={qrcodeRegionId} 
        className="w-full rounded-md overflow-hidden"
        style={{ 
          position: "relative",
          padding: "0",
          border: "1px solid #e5e7eb",
          minHeight: "300px"
        }}
      />
      <p className="text-xs text-muted-foreground text-center mt-2">
        Position the QR code within the frame to scan
      </p>
    </div>
  );
};

export default Html5QrcodePlugin;
