"use client";

import { useState } from "react";
import { signIn, signOut, useSession } from "next-auth/react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Sheet, SheetContent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import { Menu, MenuItem, HoveredLink } from "@/components/ui/navbar-menu";
import { Menu as MenuIcon, X, Store, User, CreditCard, QrCode, Home } from "lucide-react";

export function NavbarNew() {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [active, setActive] = useState<string | null>(null);

  const isActive = (path: string) => {
    return pathname === path;
  };

  return (
    <header className="sticky top-0 z-50 border-b bg-[#fbfaf9] w-full">
      <div className="container flex h-16 items-center justify-between mx-auto">
        {/* Logo */}
        <div className="flex items-center">
          <Link href="/" className="flex items-center space-x-2">
            <div className="size-7 text-[#e5ccb2] hidden sm:block">
              <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                <path clipRule="evenodd" d="M12.0799 24L4 19.2479L9.95537 8.75216L18.04 13.4961L18.0446 4H29.9554L29.96 13.4961L38.0446 8.75216L44 19.2479L35.92 24L44 28.7521L38.0446 39.2479L29.96 34.5039L29.9554 44H18.0446L18.04 34.5039L9.95537 39.2479L4 28.7521L12.0799 24Z" fill="currentColor" fillRule="evenodd"></path>
              </svg>
            </div>
            <span className="font-bold text-xl text-[#181510]">ADC Credit</span>
          </Link>
        </div>

        {/* Desktop Navigation with Aceternity Menu */}
        <div className="hidden md:flex items-center">
          <Menu setActive={setActive}>
            <MenuItem setActive={setActive} active={active} item="Dashboard">
              <div className="flex flex-col space-y-1 p-2 min-w-[200px]">
                <HoveredLink href="/dashboard">Main Dashboard</HoveredLink>
                <HoveredLink href="/dashboard/usage">Usage Statistics</HoveredLink>
                <HoveredLink href="/dashboard/analytics">Analytics</HoveredLink>
                <HoveredLink href="/dashboard/subscriptions">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    <span>Subscriptions</span>
                  </div>
                </HoveredLink>
              </div>
            </MenuItem>
            <MenuItem setActive={setActive} active={active} item="Merchant">
              <div className="flex flex-col space-y-1 p-2 min-w-[200px]">
                <HoveredLink href="/dashboard/merchant">
                  <div className="flex items-center gap-2">
                    <Store className="h-4 w-4" />
                    <span>Shops</span>
                  </div>
                </HoveredLink>
                <HoveredLink href="/dashboard/merchant/credits">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    <span>Credits</span>
                  </div>
                </HoveredLink>
                <HoveredLink href="/dashboard/merchant/codes">
                  <div className="flex items-center gap-2">
                    <QrCode className="h-4 w-4" />
                    <span>QR Codes</span>
                  </div>
                </HoveredLink>
              </div>
            </MenuItem>
            <MenuItem setActive={setActive} active={active} item="Customer">
              <div className="flex flex-col space-y-1 p-2 min-w-[200px]">
                <HoveredLink href="/dashboard/customer">
                  <div className="flex items-center gap-2">
                    <Home className="h-4 w-4" />
                    <span>Home</span>
                  </div>
                </HoveredLink>
                <HoveredLink href="/dashboard/customer/shops">
                  <div className="flex items-center gap-2">
                    <Store className="h-4 w-4" />
                    <span>Shops</span>
                  </div>
                </HoveredLink>
                <HoveredLink href="/dashboard/customer/scan">
                  <div className="flex items-center gap-2">
                    <QrCode className="h-4 w-4" />
                    <span>Scan QR</span>
                  </div>
                </HoveredLink>
                <HoveredLink href="/dashboard/customer/redeem">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    <span>Redeem Code</span>
                  </div>
                </HoveredLink>
              </div>
            </MenuItem>
            <MenuItem setActive={setActive} active={active} item="Documentation">
              <div className="flex flex-col space-y-1 p-2 min-w-[200px]">
                <HoveredLink href="/documentation">API Documentation</HoveredLink>
                <HoveredLink href="/documentation/guides">User Guides</HoveredLink>
                <HoveredLink href="/documentation/examples">Examples</HoveredLink>
              </div>
            </MenuItem>
          </Menu>
        </div>

        {/* User Menu or Sign In Button */}
        <div className="flex items-center space-x-2">
          {status === "authenticated" ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={session?.user?.image || session?.user?.picture || ''}
                      alt={session?.user?.name || "User"}
                    />
                    <AvatarFallback>
                      {session?.user?.name?.charAt(0) || "U"}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {session?.user?.name}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {session?.user?.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => signOut({ callbackUrl: "/" })}
                >
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              variant="default"
              size="sm"
              onClick={() => signIn("google", { callbackUrl: "/dashboard", redirect: true })}
              className="hidden sm:flex bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            >
              Sign In
            </Button>
          )}

          {/* Mobile Menu Button */}
          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <MenuIcon className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px] max-w-full p-0 border-l">
              <SheetTitle>
                <VisuallyHidden>Navigation Menu</VisuallyHidden>
              </SheetTitle>
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between p-4 border-b">
                  <div className="flex items-center space-x-2">
                    <div className="size-6 text-[#e5ccb2]">
                      <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <path clipRule="evenodd" d="M12.0799 24L4 19.2479L9.95537 8.75216L18.04 13.4961L18.0446 4H29.9554L29.96 13.4961L38.0446 8.75216L44 19.2479L35.92 24L44 28.7521L38.0446 39.2479L29.96 34.5039L29.9554 44H18.0446L18.04 34.5039L9.95537 39.2479L4 28.7521L12.0799 24Z" fill="currentColor" fillRule="evenodd"></path>
                      </svg>
                    </div>
                    <span className="font-bold text-lg text-[#181510]">ADC Credit</span>
                  </div>
                  <Button variant="ghost" size="icon" onClick={() => setIsMenuOpen(false)} className="h-8 w-8">
                    <X className="h-4 w-4" />
                    <span className="sr-only">Close menu</span>
                  </Button>
                </div>

                <div className="overflow-y-auto flex-1 py-2">
                  <nav className="flex flex-col">
                    {/* Dashboard Section */}
                    <div className="px-4 py-2">
                      <h3 className="text-sm font-semibold text-[#181510] mb-2">Dashboard</h3>
                      <Link
                        href="/dashboard"
                        className={`flex items-center px-4 py-2 text-sm font-medium transition-colors hover:bg-[#f1edea] rounded-md ${
                          isActive("/dashboard") ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Main Dashboard
                      </Link>
                      <Link
                        href="/dashboard/usage"
                        className={`flex items-center px-4 py-2 text-sm font-medium transition-colors hover:bg-[#f1edea] rounded-md ${
                          isActive("/dashboard/usage") ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Usage Statistics
                      </Link>
                      <Link
                        href="/dashboard/subscriptions"
                        className={`flex items-center px-4 py-2 text-sm font-medium transition-colors hover:bg-[#f1edea] rounded-md ${
                          isActive("/dashboard/subscriptions") ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <CreditCard className="h-4 w-4 mr-2" />
                        Subscriptions
                      </Link>
                    </div>

                    {/* Merchant Section */}
                    <div className="px-4 py-2 border-t border-gray-100">
                      <h3 className="text-sm font-semibold text-[#181510] mb-2">Merchant</h3>
                      <Link
                        href="/dashboard/merchant"
                        className={`flex items-center px-4 py-2 text-sm font-medium transition-colors hover:bg-[#f1edea] rounded-md ${
                          isActive("/dashboard/merchant") ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Store className="h-4 w-4 mr-2" />
                        Shops
                      </Link>
                      <Link
                        href="/dashboard/merchant/credits"
                        className={`flex items-center px-4 py-2 text-sm font-medium transition-colors hover:bg-[#f1edea] rounded-md ${
                          isActive("/dashboard/merchant/credits") ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <CreditCard className="h-4 w-4 mr-2" />
                        Credits
                      </Link>
                    </div>

                    {/* Customer Section */}
                    <div className="px-4 py-2 border-t border-gray-100">
                      <h3 className="text-sm font-semibold text-[#181510] mb-2">Customer</h3>
                      <Link
                        href="/dashboard/customer"
                        className={`flex items-center px-4 py-2 text-sm font-medium transition-colors hover:bg-[#f1edea] rounded-md ${
                          isActive("/dashboard/customer") ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Home className="h-4 w-4 mr-2" />
                        Home
                      </Link>
                      <Link
                        href="/dashboard/customer/shops"
                        className={`flex items-center px-4 py-2 text-sm font-medium transition-colors hover:bg-[#f1edea] rounded-md ${
                          isActive("/dashboard/customer/shops") ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Store className="h-4 w-4 mr-2" />
                        Shops
                      </Link>
                      <Link
                        href="/dashboard/customer/scan"
                        className={`flex items-center px-4 py-2 text-sm font-medium transition-colors hover:bg-[#f1edea] rounded-md ${
                          isActive("/dashboard/customer/scan") ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <QrCode className="h-4 w-4 mr-2" />
                        Scan QR
                      </Link>
                      <Link
                        href="/dashboard/customer/redeem"
                        className={`flex items-center px-4 py-2 text-sm font-medium transition-colors hover:bg-[#f1edea] rounded-md ${
                          isActive("/dashboard/customer/redeem") ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <CreditCard className="h-4 w-4 mr-2" />
                        Redeem Code
                      </Link>
                    </div>
                  </nav>
                </div>

                {status !== "authenticated" && (
                  <div className="p-4 border-t mt-auto">
                    <Button
                      variant="default"
                      className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                      onClick={() => {
                        setIsMenuOpen(false);
                        signIn("google", { callbackUrl: "/dashboard", redirect: true });
                      }}
                    >
                      Sign In
                    </Button>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
