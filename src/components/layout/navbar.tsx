"use client";

import { signIn, signOut, useSession } from "next-auth/react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Sheet, SheetContent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import { Menu, X } from "lucide-react";

export function Navbar() {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const isActive = (path: string) => {
    return pathname === path;
  };

  // Navigation links for authenticated users
  const authenticatedLinks = [
    { href: "/dashboard", label: "Dashboard" },
    { href: "/dashboard/merchant", label: "Merchant" },
    { href: "/dashboard/customer", label: "Customer" },
    { href: "/dashboard/api-keys", label: "API Keys" },
    { href: "/dashboard/usage", label: "Usage" },
    { href: "/dashboard/subscriptions", label: "Subscriptions" },
    { href: "/dashboard/webhooks", label: "Webhooks" },
    { href: "/dashboard/analytics", label: "Analytics" },
    { href: "/documentation", label: "Documentation" },
  ];

  // Navigation links for unauthenticated users
  const unauthenticatedLinks = [
    { href: "/dashboard", label: "Dashboard" },
    { href: "/api-keys", label: "API Keys" },
    { href: "/usage", label: "Usage" },
    { href: "/subscriptions", label: "Subscriptions" },
    { href: "/webhooks", label: "Webhooks" },
    { href: "/analytics", label: "Analytics" },
    { href: "/documentation", label: "Documentation" },
  ];

  // Get the appropriate links based on authentication status
  const navLinks = status === "authenticated" ? authenticatedLinks : unauthenticatedLinks;

  return (
    <header className="sticky top-0 z-50 border-b bg-[#fbfaf9] w-full">
      <div className="container flex h-16 items-center justify-between mx-auto">
        {/* Logo */}
        <div className="flex items-center">
          <Link href="/" className="flex items-center space-x-2">
            <div className="size-7 text-[#e5ccb2] hidden sm:block">
              <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                <path clipRule="evenodd" d="M12.0799 24L4 19.2479L9.95537 8.75216L18.04 13.4961L18.0446 4H29.9554L29.96 13.4961L38.0446 8.75216L44 19.2479L35.92 24L44 28.7521L38.0446 39.2479L29.96 34.5039L29.9554 44H18.0446L18.04 34.5039L9.95537 39.2479L4 28.7521L12.0799 24Z" fill="currentColor" fillRule="evenodd"></path>
              </svg>
            </div>
            <span className="font-bold text-xl text-[#181510]">ADC Credit</span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-4">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className={`text-sm font-medium transition-colors hover:text-[#8a745c] ${
                isActive(link.href) ? "text-[#181510] font-semibold" : "text-[#8a745c]"
              }`}
            >
              {link.label}
            </Link>
          ))}
        </nav>

        {/* User Menu or Sign In Button */}
        <div className="flex items-center space-x-2">
          {status === "authenticated" ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={session?.user?.image || session?.user?.picture || ''}
                      alt={session?.user?.name || "User"}
                    />
                    <AvatarFallback>
                      {session?.user?.name?.charAt(0) || "U"}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {session?.user?.name}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {session?.user?.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => signOut({ callbackUrl: "/" })}
                >
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              variant="default"
              size="sm"
              onClick={() => signIn("google", { callbackUrl: "/dashboard", redirect: true })}
              className="hidden sm:flex bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
            >
              Sign In
            </Button>
          )}

          {/* Mobile Menu Button */}
          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px] max-w-full p-0 border-l">
              <SheetTitle>
                <VisuallyHidden>Navigation Menu</VisuallyHidden>
              </SheetTitle>
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between p-4 border-b">
                  <div className="flex items-center space-x-2">
                    <div className="size-6 text-[#e5ccb2]">
                      <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <path clipRule="evenodd" d="M12.0799 24L4 19.2479L9.95537 8.75216L18.04 13.4961L18.0446 4H29.9554L29.96 13.4961L38.0446 8.75216L44 19.2479L35.92 24L44 28.7521L38.0446 39.2479L29.96 34.5039L29.9554 44H18.0446L18.04 34.5039L9.95537 39.2479L4 28.7521L12.0799 24Z" fill="currentColor" fillRule="evenodd"></path>
                      </svg>
                    </div>
                    <span className="font-bold text-lg text-[#181510]">ADC Credit</span>
                  </div>
                  <Button variant="ghost" size="icon" onClick={() => setIsMenuOpen(false)} className="h-8 w-8">
                    <X className="h-4 w-4" />
                    <span className="sr-only">Close menu</span>
                  </Button>
                </div>

                <div className="overflow-y-auto flex-1 py-2">
                  <nav className="flex flex-col">
                    {navLinks.map((link) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        className={`flex items-center px-4 py-3 text-base font-medium transition-colors hover:bg-[#f1edea] ${
                          isActive(link.href) ? "text-[#181510] font-semibold bg-[#f1edea]" : "text-[#8a745c]"
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {link.label}
                      </Link>
                    ))}
                  </nav>
                </div>

                {status !== "authenticated" && (
                  <div className="p-4 border-t mt-auto">
                    <Button
                      variant="default"
                      className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d9b99a]"
                      onClick={() => {
                        setIsMenuOpen(false);
                        signIn("google", { callbackUrl: "/dashboard", redirect: true });
                      }}
                    >
                      Sign In
                    </Button>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
