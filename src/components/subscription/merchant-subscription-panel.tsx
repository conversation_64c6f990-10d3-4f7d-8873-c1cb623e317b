"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Subscription, SubscriptionTier, MerchantShop } from "@/types";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  useGetMerchantShopsQuery,
  useGetSubscriptionTiersQuery
} from "@/lib/api/apiSlice";
import { ShoppingBag, CreditCard, Check, AlertCircle, Users, BarChart, Zap, Globe, Shield } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface MerchantSubscriptionPanelProps {
  subscriptions: Subscription[];
  createSubscription: (data: any) => Promise<any>;
}

export default function MerchantSubscriptionPanel({ subscriptions, createSubscription: createSubscriptionFn }: MerchantSubscriptionPanelProps) {
  const [selectedShop, setSelectedShop] = useState<string | null>(null);
  const [selectedTier, setSelectedTier] = useState<SubscriptionTier | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const router = useRouter();

  // Check if the shop has an active subscription
  const hasActiveSubscription = (shopId?: string) => {
    if (!shopId && !selectedShop) return false;
    const shopToCheck = shopId || selectedShop;
    return subscriptions.some((sub: Subscription) =>
      sub.status === 'active' &&
      sub.subscription_type === 'merchant' &&
      sub.merchant_shop_id === shopToCheck
    );
  };

  // Check if a specific tier is already active for the selected shop
  const isTierActive = (tierId: number) => {
    if (!selectedShop) return false;
    return subscriptions.some((sub: Subscription) =>
      sub.status === 'active' &&
      sub.subscription_type === 'merchant' &&
      sub.merchant_shop_id === selectedShop &&
      sub.subscription_tier_id === tierId
    );
  };

  // Fetch merchant shops
  const {
    data: shops = [],
    isLoading: isLoadingShops,
  } = useGetMerchantShopsQuery();

  // Fetch subscription tiers
  const {
    data: subscriptionTiers = [],
    isLoading: isLoadingTiers
  } = useGetSubscriptionTiersQuery();

  // We'll use the createSubscriptionFn passed as prop instead of the mutation

  // Find the selected shop
  const shop = shops.find(s => s.id === selectedShop);

  // Handle shop selection
  const handleShopSelect = (shopId: string) => {
    setSelectedShop(shopId);
  };

  // Define merchant subscription tiers
  const merchantTiers = [
    {
      id: 1,
      name: "Merchant Basic",
      description: "Essential features for small businesses",
      price: 9.99,
      credit_limit: 1000,
      features: [
        "Up to 50 customers",
        "Basic credit management",
        "1,000 monthly credit transactions",
        "Basic analytics",
        "Single shop/location",
        "Email support",
        "Standard QR code generation"
      ],
      icon: <Users className="h-5 w-5 text-blue-500" />
    },
    {
      id: 2,
      name: "Merchant Pro",
      description: "Advanced features for growing businesses",
      price: 29.99,
      credit_limit: 10000,
      features: [
        "Up to 500 customers",
        "Advanced credit management",
        "10,000 monthly credit transactions",
        "Enhanced analytics",
        "Up to 3 shops/locations",
        "Priority email support",
        "Custom QR code branding",
        "API access for basic integrations",
        "Bulk credit operations"
      ],
      icon: <BarChart className="h-5 w-5 text-purple-500" />
    },
    {
      id: 3,
      name: "Merchant Enterprise",
      description: "Complete solution for large businesses",
      price: 99.99,
      credit_limit: 50000,
      features: [
        "Unlimited customers",
        "Complete credit management suite",
        "Unlimited monthly credit transactions",
        "Advanced analytics and reporting",
        "Unlimited shops/locations",
        "24/7 dedicated support",
        "White-labeled QR codes",
        "Full API access with higher rate limits",
        "Multi-user access with permissions",
        "Custom integration support"
      ],
      icon: <Globe className="h-5 w-5 text-green-500" />
    }
  ];

  // Handle upgrade confirmation
  const handleUpgradeConfirm = async () => {
    if (!selectedShop || !selectedTier) return;

    setIsUpgrading(true);
    try {
      // For free tiers, create subscription directly
      if (selectedTier.price === 0) {
        await createSubscriptionFn({
          subscription_tier_id: selectedTier.id,
          auto_renew: true,
          subscription_type: "merchant",
          merchant_shop_id: selectedShop
        });

        toast.success(`Successfully upgraded ${shop?.name} to ${selectedTier.name} plan`);
        setConfirmDialogOpen(false);
      } else {
        // For paid tiers, create a Stripe checkout session using the backend API
        const response = await fetch('/api/stripe/create-checkout-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tier_id: selectedTier.id,
            subscription_type: 'merchant',
            merchant_shop_id: selectedShop,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create checkout session');
        }

        const data = await response.json();

        // Check if it's a free tier response from the backend
        if (data.free_tier) {
          // For free tiers, create subscription directly
          await createSubscriptionFn({
            subscription_tier_id: selectedTier.id,
            auto_renew: true,
            subscription_type: "merchant",
            merchant_shop_id: selectedShop
          });

          toast.success(`Successfully upgraded ${shop?.name} to ${selectedTier.name} plan`);
          setConfirmDialogOpen(false);
          return;
        }

        if (!data.checkout_url) {
          throw new Error('Failed to create checkout session');
        }

        // Close the dialog
        setConfirmDialogOpen(false);

        // Redirect to Stripe Checkout
        window.location.href = data.checkout_url;
      }
    } catch (error) {
      toast.error("Failed to upgrade subscription plan");
      console.error("Error upgrading subscription:", error);
    } finally {
      setIsUpgrading(false);
    }
  };

  // Loading state
  if (isLoadingShops || isLoadingTiers) {
    return (
      <Card className="mb-8 border shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle>Merchant Subscription Plans</CardTitle>
          <CardDescription>
            Manage subscription plans for your merchant shops
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // No shops state
  if (shops.length === 0) {
    return (
      <Card className="mb-8 border shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle>Merchant Subscription Plans</CardTitle>
          <CardDescription>
            Manage subscription plans for your merchant shops
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              You don't have any merchant shops yet. Create a shop to manage subscription plans.
            </AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/dashboard/merchant/new')}>
            <ShoppingBag className="h-4 w-4 mr-2" />
            Create Shop
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-8 border shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle>Merchant Subscription Plans</CardTitle>
        <CardDescription>
          Manage subscription plans for your merchant shops
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <label className="text-sm font-medium mb-2 block">Select Shop</label>
          <Select value={selectedShop || ""} onValueChange={handleShopSelect}>
            <SelectTrigger>
              <SelectValue placeholder="Select a shop" />
            </SelectTrigger>
            <SelectContent>
              {shops.map((shop) => (
                <SelectItem key={shop.id} value={shop.id}>
                  {shop.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedShop && shop && (
          <>
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">{shop.name}</h3>
              <p className="text-sm text-muted-foreground mb-4">{shop.description}</p>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex flex-col space-y-1">
                  <span className="text-sm text-muted-foreground">Contact Email</span>
                  <span>{shop.contact_email || "N/A"}</span>
                </div>
                <div className="flex flex-col space-y-1">
                  <span className="text-sm text-muted-foreground">Contact Phone</span>
                  <span>{shop.contact_phone || "N/A"}</span>
                </div>
              </div>
            </div>

            <Separator className="my-6" />

            <h3 className="text-lg font-semibold mb-4">Merchant Subscription Plans</h3>
            <div className="grid gap-6 md:grid-cols-3">
              {merchantTiers.map((tier) => (
                <Card
                  key={tier.id}
                  className={`border cursor-pointer transition-all ${selectedTier?.id === tier.id ? 'border-primary ring-2 ring-primary/20' : 'hover:border-primary/50'}`}
                  onClick={() => setSelectedTier(tier as unknown as SubscriptionTier)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{tier.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        {isTierActive(tier.id) && (
                          <Badge className="bg-green-500 hover:bg-green-600">Current</Badge>
                        )}
                        {tier.icon}
                      </div>
                    </div>
                    <CardDescription>{tier.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold mb-2">
                      ${tier.price.toFixed(2)}
                      <span className="text-sm font-normal text-muted-foreground">
                        /month
                      </span>
                    </div>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span>Credits:</span>
                        <span className="font-medium">
                          {tier.credit_limit.toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <ul className="space-y-2 text-sm">
                      {tier.features.map((featureStr, index) => {
                        // Parse the feature string which is in JSON format
                        try {
                          // Remove the curly braces and parse as an array
                          const cleanedStr = featureStr.replace(/^\{|\}$/g, '');
                          const featuresArray = JSON.parse(`[${cleanedStr}]`);

                          return featuresArray.map((feature: string, innerIndex: number) => (
                            <li key={`${index}-${innerIndex}`} className="flex items-start">
                              <Check className="h-4 w-4 mr-2 text-green-500 mt-0.5" />
                              <span>{feature}</span>
                            </li>
                          ));
                        } catch {
                          // Fallback to displaying the raw string if parsing fails
                          return (
                            <li key={index} className="flex items-start">
                              <Check className="h-4 w-4 mr-2 text-green-500 mt-0.5" />
                              <span>{featureStr}</span>
                            </li>
                          );
                        }
                      })}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>

            {selectedTier && (
              <div className="mt-6 flex justify-end">
                <Button
                  onClick={() => setConfirmDialogOpen(true)}
                  disabled={hasActiveSubscription() || isTierActive(selectedTier.id)}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  {isTierActive(selectedTier.id)
                    ? "Current Plan"
                    : hasActiveSubscription()
                      ? "Already Subscribed"
                      : `Upgrade to ${selectedTier.name}`}
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>

      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Merchant Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to subscribe {shop?.name} to the {selectedTier?.name} plan?
              This will be billed at ${selectedTier?.price.toFixed(2)}/month.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <h4 className="font-medium mb-2">Plan Details:</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex justify-between">
                <span>Monthly Price:</span>
                <span className="font-medium">${selectedTier?.price.toFixed(2)}</span>
              </li>
              <li className="flex justify-between">
                <span>Credits:</span>
                <span className="font-medium">{selectedTier?.credit_limit.toLocaleString()}</span>
              </li>
              <li className="flex justify-between">
                <span>Type:</span>
                <span className="font-medium">Merchant Subscription</span>
              </li>
            </ul>

            <Alert className="mt-4">
              <AlertDescription>
                This subscription will be linked to your merchant shop and will provide merchant-specific features.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleUpgradeConfirm} disabled={isUpgrading}>
              {isUpgrading ? "Processing..." : "Confirm Subscription"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
