#!/bin/bash

# This script seeds usage data for testing the real usage functionality
# It makes API calls to the /external/consume endpoint to simulate real API usage

# Configuration
API_URL=${API_URL:-"http://localhost:8080/api/v1"}
API_KEY=${API_KEY:-""}
NUM_REQUESTS=${NUM_REQUESTS:-50}

# Check if API key is provided
if [ -z "$API_KEY" ]; then
  echo "Error: API_KEY environment variable is required"
  echo "Usage: API_KEY=your_api_key [NUM_REQUESTS=50] [API_URL=http://localhost:8080/api/v1] ./seed_usage.sh"
  exit 1
fi

echo "Seeding $NUM_REQUESTS usage records to $API_URL using API key $API_KEY"

# Sample endpoints and methods
ENDPOINTS=(
  "/users"
  "/products"
  "/orders"
  "/customers"
  "/analytics"
  "/search"
  "/auth/login"
  "/auth/register"
  "/payments/process"
  "/files/upload"
)

METHODS=(
  "GET"
  "POST"
  "PUT"
  "DELETE"
)

# Sample user agents
USER_AGENTS=(
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
  "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
  "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
)

# Sample IP addresses
IP_ADDRESSES=(
  "***********"
  "********"
  "**********"
  "*******"
  "*******"
)

# Function to generate a random number between min and max
random_number() {
  min=$1
  max=$2
  echo $((RANDOM % (max - min + 1) + min))
}

# Function to get a random element from an array
random_element() {
  array=("$@")
  index=$((RANDOM % ${#array[@]}))
  echo "${array[$index]}"
}

# Create usage records
for i in $(seq 1 $NUM_REQUESTS); do
  # Generate random data
  endpoint=$(random_element "${ENDPOINTS[@]}")
  method=$(random_element "${METHODS[@]}")
  credits=$(random_number 1 10)
  ip_address=$(random_element "${IP_ADDRESSES[@]}")
  user_agent=$(random_element "${USER_AGENTS[@]}")
  
  # Create JSON payload
  json_data=$(cat <<EOF
{
  "endpoint": "$endpoint",
  "method": "$method",
  "credits": $credits,
  "ip_address": "$ip_address",
  "user_agent": "$user_agent"
}
EOF
)

  # Make API call
  echo "Creating usage record $i/$NUM_REQUESTS: $method $endpoint ($credits credits)"
  response=$(curl -s -X POST "$API_URL/external/consume" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: $API_KEY" \
    -d "$json_data")
  
  # Check response
  if [[ $response == *"credit_balance"* ]]; then
    echo "Success: $response"
  else
    echo "Error: $response"
  fi
  
  # Add a small delay to avoid rate limiting
  sleep 0.2
done

echo "Finished seeding $NUM_REQUESTS usage records"
