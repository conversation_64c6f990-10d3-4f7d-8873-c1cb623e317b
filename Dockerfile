# Stage 1: Build the Next.js application
FROM oven/bun:1 AS builder

WORKDIR /app

# Copy package.json and bun.lock
COPY package.json ./
COPY bun.lock ./

# Install dependencies
RUN bun install --frozen-lockfile

# Copy the rest of the application code
COPY . .

# Set environment variables for build
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV SKIP_TYPE_CHECK=true
ENV NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51QtW5QCtNXkGk5bXRPphcrlMF1dWoY0zbVHB5fgCjD1fs0jggEXIG8FeFDP20CBLiUdg2fCCVkqoUujwefOM6wHu00vvyWqS4V
ENV STRIPE_SECRET_KEY=sk_test_51QtW5QCtNXkGk5bXQrIg3IdxRXAsB6a5XBKbpxQiTlgdU1GN3Pq5deYt0gtq6mX2GKOnjNMaQaq8nWHvhMTNv5sh00PnRaVKrg
ENV STRIPE_WEBHOOK_SECRET=whsec_53b8168c6650ebad6a2d60e0efcc5aafbe5a87e6e82b757fe719ca04f61f483a

# Build the Next.js application with custom build script that bypasses linting and type checking
RUN bun run next build --no-lint

# Stage 2: Create a minimal runtime image
FROM oven/bun:1-slim

WORKDIR /app

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3800

# Copy built application from the builder stage
COPY --from=builder /app/package.json ./
COPY --from=builder /app/next.config.ts ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules

# Expose the port
EXPOSE 3800

# Start the Next.js server
CMD ["bun", "start"]
