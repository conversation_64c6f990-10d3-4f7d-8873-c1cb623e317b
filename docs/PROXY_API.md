# Next.js Proxy API Documentation

This document explains how to use the Next.js proxy API route that forwards requests to the backend Golang API.

## Overview

The proxy API route is located at `/api/v1/[...service]/route.ts` and provides a seamless way to forward frontend requests to the backend API while handling authentication, CORS, and other concerns automatically.

## Features

- **Automatic Authentication**: Forwards JWT tokens from session/localStorage
- **All HTTP Methods**: Supports GET, POST, PUT, DELETE, PATCH, OPTIONS
- **Path Preservation**: Maintains the exact API path structure
- **Query Parameters**: Forwards all query parameters
- **Request Headers**: Forwards relevant headers like User-Agent, Accept, etc.
- **CORS Handling**: Automatically handles CORS preflight requests
- **Error Handling**: Provides detailed error responses
- **Logging**: Comprehensive logging for debugging

## Usage

### Direct Fetch Requests

You can make requests directly to the proxy endpoint:

```typescript
// GET request
const response = await fetch('/api/v1/users/me', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}` // Optional, will use session token if not provided
  }
});

// POST request
const response = await fetch('/api/v1/organizations', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'My Organization',
    description: 'A test organization'
  })
});
```

### Using the Proxy Client

For convenience, use the `proxyApi` client from `@/lib/api/proxy-client`:

```typescript
import { proxyApi } from '@/lib/api/proxy-client';

// GET request
const user = await proxyApi.get('/users/me');

// POST request
const organization = await proxyApi.post('/organizations', {
  name: 'My Organization',
  description: 'A test organization'
});

// PUT request
const updatedUser = await proxyApi.put('/users/me', {
  name: 'Updated Name'
});

// DELETE request
await proxyApi.delete('/organizations/my-org-slug');
```

### Using Example Functions

The proxy client includes pre-built functions for common endpoints:

```typescript
import { proxyExamples } from '@/lib/api/proxy-client';

// User operations
const currentUser = await proxyExamples.getCurrentUser();
const updatedUser = await proxyExamples.updateUser({ name: 'New Name' });

// Organization operations
const organizations = await proxyExamples.getOrganizations();
const newOrg = await proxyExamples.createOrganization({
  name: 'Test Org',
  description: 'Test Description'
});

// API Key operations
const apiKeys = await proxyExamples.getApiKeys();
const newApiKey = await proxyExamples.createApiKey({
  name: 'Test API Key'
});

// Merchant operations
const shops = await proxyExamples.getMerchantShops();
const newShop = await proxyExamples.createMerchantShop({
  name: 'Test Shop',
  description: 'Test Shop Description'
});
```

## URL Mapping

The proxy automatically maps frontend URLs to backend URLs:

| Frontend URL | Backend URL |
|--------------|-------------|
| `/api/v1/users/me` | `http://backend:8080/api/v1/users/me` |
| `/api/v1/organizations` | `http://backend:8080/api/v1/organizations` |
| `/api/v1/merchant-shops/123` | `http://backend:8080/api/v1/merchant-shops/123` |
| `/api/v1/apikeys?limit=10` | `http://backend:8080/api/v1/apikeys?limit=10` |

## Authentication

The proxy handles authentication automatically by:

1. **Checking Authorization header**: If present, forwards the Bearer token
2. **Using session token**: Gets token from NextAuth session
3. **Fallback to stored token**: Uses token from session/localStorage

You don't need to manually handle authentication when using the proxy client.

## Error Handling

The proxy provides detailed error responses:

```json
{
  "error": "Proxy request failed",
  "message": "HTTP 404: Not Found",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

Common error scenarios:
- **401 Unauthorized**: Invalid or missing authentication token
- **404 Not Found**: Endpoint doesn't exist on backend
- **500 Internal Server Error**: Backend server error or proxy error

## Environment Configuration

The proxy uses the following environment variable:

```env
NEXT_PUBLIC_BACKEND_URL=http://localhost:8080
```

Make sure this points to your backend API server.

## Testing

Visit `/test-proxy` to access the test page where you can:

- Test common API endpoints with pre-built buttons
- Make custom requests with any HTTP method
- View detailed request/response information
- Debug authentication and error scenarios

## CORS

The proxy automatically handles CORS by:

- Adding appropriate CORS headers to all responses
- Handling OPTIONS preflight requests
- Allowing all origins (configurable if needed)

## Logging

The proxy provides comprehensive logging:

```
Proxying GET request to: http://localhost:8080/api/v1/users/me
Using auth token: eyJhbGciOiJIUzI1NiIs...
Backend response status: 200
```

Check the browser console and server logs for debugging information.

## Best Practices

1. **Use the proxy client**: Prefer `proxyApi` over direct fetch calls
2. **Handle errors**: Always wrap API calls in try-catch blocks
3. **Use TypeScript**: Define types for your API responses
4. **Check authentication**: Ensure users are logged in before making authenticated requests
5. **Monitor logs**: Use the logging output for debugging issues

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check if user is logged in
   - Verify token is stored in session/localStorage
   - Check backend authentication middleware

2. **CORS Errors**
   - Ensure you're using the proxy route, not direct backend calls
   - Check that NEXT_PUBLIC_BACKEND_URL is correctly set

3. **404 Not Found**
   - Verify the endpoint exists on the backend
   - Check the URL path is correct
   - Ensure backend server is running

4. **500 Internal Server Error**
   - Check backend server logs
   - Verify environment variables are set
   - Check network connectivity to backend
