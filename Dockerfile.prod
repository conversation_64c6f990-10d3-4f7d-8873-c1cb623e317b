# Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Create a .env.production file with the environment variables
RUN echo "NEXT_PUBLIC_BACKEND_URL=https://adc-credit-backend-457647006078.us-central1.run.app" > .env.production && \
    echo "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51QtW5QCtNXkGk5bXRPphcrlMF1dWoY0zbVHB5fgCjD1fs0jggEXIG8FeFDP20CBLiUdg2fCCVkqoUujwefOM6wHu00vvyWqS4V" >> .env.production && \
    echo "STRIPE_SECRET_KEY=sk_test_51QtW5QCtNXkGk5bXQrIg3IdxRXAsB6a5XBKbpxQiTlgdU1GN3Pq5deYt0gtq6mX2GKOnjNMaQaq8nWHvhMTNv5sh00PnRaVKrg" >> .env.production && \
    echo "STRIPE_WEBHOOK_SECRET=whsec_53b8168c6650ebad6a2d60e0efcc5aafbe5a87e6e82b757fe719ca04f61f483a" >> .env.production

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy built assets from the builder stage
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/public ./public

# Expose the port
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Start the application
CMD ["npm", "start"]
